package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.feign.BatchEngineFeignService;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.entity.CodeSubTasks;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.out.request.OutIndicatorTimeRangeRequest;
import com.trs.ai.moye.out.response.StatisticPeriodResponse;
import com.trs.ai.moye.out.service.OutIndicatorService;
import com.trs.ai.moye.storageengine.feign.DatabaseConnectionFeign;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.CodeParameterItem;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2025/6/25 17:44
 */
@Service
@Slf4j
public class IndicatorImportService {

    @Resource
    private DatabaseConnectionFeign databaseConnectionFeign;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private OutIndicatorService outIndicatorService;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private BatchEngineFeignService batchEngineFeignService;

    @Resource
    private BatchArrangementMapper batchArrangementMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;


    /**
     * 导入指标数据
     *
     * @param files     上传的文件
     * @param timeRange 时间范围
     * @param category  业务分类
     * @return 是否导入成功
     */
    public ResponseMessage importIndicatorData(MultipartFile[] files, String timeRange, Integer category) {
        OutIndicatorTimeRangeRequest timeRangeRequest = JsonUtils.parseObject(timeRange,
            OutIndicatorTimeRangeRequest.class);
        StatisticPeriodResponse response = outIndicatorService.getStatisticPeriod(timeRangeRequest);
        LocalDateTime fileBeginTime = response.getBeginTime();
        LocalDateTime fileEndTime = response.getEndTime();

        List<String> results = new ArrayList<>();
        List<Integer> updatedDataModelIds = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                Integer updatedDataModelId = saveFileInfo(file, fileBeginTime, fileEndTime, category);
                updatedDataModelIds.add(updatedDataModelId);
                results.add("文件 " + file.getOriginalFilename() + " 导入成功，数据模型ID: " + updatedDataModelId);
            } catch (Exception e) {
                log.error("保存文件数据失败: {}", e.getMessage(), e);
                results.add("文件 " + file.getOriginalFilename() + " 保存失败: " + e.getMessage());
                return ResponseMessage.error("", results);
            }

        }
        // 如果保存成功，更新批处理任务
        BatchExecuteResult executeResult = updateBatchTasks(updatedDataModelIds, fileBeginTime, fileEndTime);
        results.addAll(executeResult.getResults());
        return executeResult.getSuccess() ? ResponseMessage.ok(results) :
            ResponseMessage.error("", results);

    }

    /**
     * 保存文件数据到对应的贴源表中
     *
     * @param file          文件
     * @param fileBeginTime 文件开始时间
     * @param fileEndTime   文件结束时间
     * @param category      业务分类
     * @return 更新的模型id
     * @throws IOException io异常
     */
    private Integer saveFileInfo(MultipartFile file, LocalDateTime fileBeginTime, LocalDateTime fileEndTime,
        Integer category) throws IOException {
        InputStream inputStream = file.getInputStream();
        String fileName = file.getOriginalFilename();
        log.info("开始保存文件：{} 到业务分类：{}", file, category);
        // 解析文件到表信息
        TableInfo tableInfo = parseInputStreamToTableInfo(fileName, inputStream);
        // 根据文件字段匹配对应的贴源表
        DataModel dataModel = getDataModelFromFile(tableInfo, category);
        List<DataStorage> dataStorages = dataModel.getDataStorages();
        if (dataStorages == null || dataStorages.isEmpty()) {
            log.error("数据模型{}未配置存储", dataModel.getEnName());
            throw new BizException("数据模型未配置存储");
        }
        Integer connectionId = dataStorages.get(0).getConnectionId();
        tableInfo.setConnectionId(connectionId);

        // 加入审计字段
        tableInfo.getFields().add(new SearchableField(
            "开始时间", "file_begin_time", FieldType.DATETIME, "文件数据开始时间", dataModel.getId(), false, null));
        tableInfo.getFields().add(new SearchableField(
            "结束时间", "file_end_time", FieldType.DATETIME, "文件数据结束时间", dataModel.getId(), false, null));
        tableInfo.getFields().add(new SearchableField(
            "文件名", "file_name", FieldType.STRING, "文件名", dataModel.getId(), false, null));
        for (Map<String, Object> data : tableInfo.getData()) {
            data.put("file_begin_time", fileBeginTime);
            data.put("file_end_time", fileEndTime);
            data.put("file_name", fileName);
        }
        tableInfo.setOldDataFilter(Map.of("file_begin_time", DateTimeUtils.formatStr(fileBeginTime),
            "file_end_time", DateTimeUtils.formatStr(fileEndTime)));
        boolean saveResult = databaseConnectionFeign.saveFileToStorage(connectionId, tableInfo);
        log.info("导入文件数据 {} 到数据建模 {} 结果: {}", fileName, dataModel.getId(), saveResult);
        return dataModel.getId();
    }

    /**
     * 执行批处理任务；父任务执行完成后，再执行依赖父任务的子任务
     *
     * @param parentDataModelIds 父任务id列表
     * @param beginTime          开始时间
     * @param endTime            结束时间
     * @return 执行结果
     */
    public BatchExecuteResult updateBatchTasks(List<Integer> parentDataModelIds, LocalDateTime beginTime,
        LocalDateTime endTime) {
        List<DataModel> dataModels = new ArrayList<>();
        // 获取所有用到给定数据模型的下游批处理任务
        for (Integer parentId : parentDataModelIds) {
            List<DataSourceConfig> sourceConfigs = dataSourceConfigMapper.selectBySourceModelId(parentId);
            dataModels.addAll(dataModelMapper.selectByIds(
                    sourceConfigs.stream().map(DataSourceConfig::getDataModelId).distinct().toList())
                .stream().filter(dataModel -> !dataModel.isRealTimeTask()).distinct().toList());
        }

        List<String> resultInfos = new ArrayList<>();

        // 立即执行所有批处理任务
        List<TaskExecuteResult> results = executeBatchTasks(dataModels, beginTime, endTime);

        // 收集失败任务，进行重试
        List<Integer> failedDataModelIds = new ArrayList<>();
        for (TaskExecuteResult result : results) {
            resultInfos.add(result.createTaskInfo());
            if (!result.getSuccess()) {
                failedDataModelIds.add(result.getDataModelId());
            }
        }
        List<TaskExecuteResult> rerunResults = executeBatchTasks(
            dataModels.stream().filter(dataModel -> failedDataModelIds.contains(dataModel.getId()))
                .collect(Collectors.toList()),
            beginTime, endTime);

        //重试后仍失败，则直接返回
        for (TaskExecuteResult result : rerunResults) {
            resultInfos.add("重试：" + result.createTaskInfo());
        }
        if (rerunResults.stream().anyMatch(result -> !result.getSuccess())) {
            return new BatchExecuteResult(false, resultInfos);
        }

        // 查找并执行依赖此批处理任务的其他任务
        if (!dataModels.isEmpty()) {
            BatchExecuteResult childrenResult = updateBatchTasks(dataModels.stream().map(DataModel::getId).toList(),
                beginTime, endTime);
            resultInfos.addAll(childrenResult.getResults());
            return new BatchExecuteResult(childrenResult.getSuccess(), resultInfos);
        }

        return new BatchExecuteResult(true, resultInfos);
    }

    @Data
    @AllArgsConstructor
    private static class BatchExecuteResult {

        private Boolean success;
        private List<String> results;
    }

    @Data
    @AllArgsConstructor
    private static class TaskExecuteResult {

        private Integer dataModelId;
        private String taskName;
        private Boolean success;
        private String message;

        public String createTaskInfo() {
            return "批处理任务[" + taskName + (success ? "]执行成功" : "]执行失败: " + message);
        }
    }

    private List<TaskExecuteResult> executeBatchTasks(List<DataModel> dataModels, LocalDateTime beginTime,
        LocalDateTime endTime) {
        List<CompletableFuture<TaskExecuteResult>> futures = dataModels.stream()
            .map(dataModel -> CompletableFuture.supplyAsync(() -> {
                Integer dataModelId = dataModel.getId();
                String taskName = dataModel.getLayer().getLabel() + "-" + dataModel.getZhName();
                try {
                    ResponseMessage response = executeTask(dataModel, beginTime, endTime);
                    return new TaskExecuteResult(
                        dataModelId, taskName, response.isSuccess(), response.getMessage());
                } catch (Exception e) {
                    log.error("批处理任务 {} 执行失败: {}", dataModel.getZhName(), e.getMessage(), e);
                    return new TaskExecuteResult(dataModelId, taskName, false, e.getMessage());
                }
            })).collect(Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(
                v -> futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList())
            ).join();
    }

    private ResponseMessage executeTask(DataModel dataModel, LocalDateTime beginTime, LocalDateTime endTime) {
        Map<String, String> params = Map.of(
            "beginTime", DateTimeUtils.formatStr(beginTime),
            "endTime", DateTimeUtils.formatStr(endTime));
        Integer dataModelId = dataModel.getId();
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(dataModelId);
        BatchProcessSparkConfig sparkConfig = executeConfig.getSparkConfig();
        List<CodeParameterItem> codeParams = params.entrySet()
            .stream().map(entry -> new CodeParameterItem(entry.getKey(), entry.getValue()))
            .collect(Collectors.toList());
        sparkConfig.setCustomCodeParameters(codeParams);

        // 同步调用，等上一批执行完成后才执行下一批
        if (dataModel.getLayer().equals(ModelLayer.INDICATOR)
            || batchArrangement.getDisplayType().equals(ArrangeDisplayType.CANVAS)) {
            //dag
            BatchEngineTaskParam task = new BatchEngineTaskParam(dataModelId.toString(),
                dataModel.getLayer(),
                dataModel.getZhName());
            task.setSparkConfig(executeConfig.getSparkConfig());
            // 指标库任务传入执行指定过滤时间
            task.setBeginTime(beginTime);
            task.setEndTime(endTime);
            return batchEngineFeignService.dagExecuteSync(task);
        } else {
            // code
            CodeSubTasks[] codeSubTasks = batchArrangement.getCodeSubTasks();
            List<BatchEngineTaskParam> tasks = Arrays.stream(codeSubTasks).map(codeSubTask -> {
                BatchEngineTaskParam vo = new BatchEngineTaskParam(String.valueOf(dataModelId),
                    dataModel.getLayer(), dataModel.getZhName());
                vo.setCode(codeSubTask.getCode());
                vo.setSubTaskName(codeSubTask.getName());
                vo.setCodeType(codeSubTask.getCodeType());
                vo.setSparkConfig(executeConfig.getSparkConfig());
                return vo;
            }).toList();
            return batchEngineFeignService.executeSync(tasks);
        }
    }

    private DataModel getDataModelFromFile(TableInfo tableInfo, Integer category) {
        List<DataModel> dataModels = dataModelMapper.selectByLayerAndCategoryId(ModelLayer.ODS, category);
        int matchCount = 0;
        DataModel matchedDataModel = null;
        Map<SearchableField, String> matchedFields = new HashMap<>();
        for (DataModel dataModel : dataModels) {
            int currentMatchCount = 0;
            Map<SearchableField, String> currentFields = new HashMap<>();
            for (SearchableField field : tableInfo.getFields()) {
                // 根据文件表头匹配表字段中文名
                String enName = getFieldEnName(field.getZhName(), dataModel.getFields());
                if (enName != null) {
                    currentFields.put(field, enName);
                    currentMatchCount++;
                }
            }
            if (currentMatchCount > matchCount) {
                matchedFields.clear();
                matchedFields.putAll(currentFields);
                matchCount = currentMatchCount;
                matchedDataModel = dataModel;
            }
        }

        // 返回匹配字段最多的数据模型
        if (matchCount > 0) {
            List<SearchableField> allMatchedFields = matchedFields.entrySet().stream().map(entry -> {
                SearchableField field = entry.getKey();
                field.setEnName(entry.getValue());
                return field;
            }).collect(Collectors.toList());
            tableInfo.setFields(allMatchedFields);
            tableInfo.setEnName(matchedDataModel.getEnName());
            // 将数据中的中文字段名替换成英文名
            List<Map<String, Object>> newDataRows = new ArrayList<>();
            for (Map<String, Object> data : tableInfo.getData()) {
                Map<String, Object> newData = new HashMap<>();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    Optional<String> enName = tableInfo.getFields().stream()
                        .filter(field -> field.getZhName().equals(entry.getKey()))
                        .findFirst()
                        .map(SearchableField::getEnName);
                    enName.ifPresent(s -> newData.put(s, entry.getValue()));
                }
                newDataRows.add(newData);
            }
            tableInfo.setData(newDataRows);
            return matchedDataModel;
        }

        throw new BizException("未找到与文件 " + tableInfo.getZhName() + " 字段匹配的数据表，无法保存数据！");
    }

    /**
     * 从输入流解析文件为TableInfo
     *
     * @param fileName    文件名
     * @param inputStream 输入流
     * @return 表信息对象
     */
    public TableInfo parseInputStreamToTableInfo(String fileName, InputStream inputStream) {
        log.info("开始解析文件: {}", fileName);
        try {
            String fileExtension = FilenameUtils.getExtension(fileName);
            if (fileExtension.equals("xlsx") || fileExtension.equals("xls")) {
                log.info("解析Excel文件: {}", fileName);
            } else {
                log.error("不支持的文件类型: {}", fileExtension);
                throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
            }

            // 提取表基本信息
            TableInfo tableInfo = new TableInfo();

            tableInfo.setZhName(fileName);
            tableInfo.setDescription(fileName);

            // 根据文件类型选择解析方法
            Wrapper wrapper = parseExcel(fileExtension, inputStream);
            tableInfo.setFields(wrapper.getHeaders());
            tableInfo.setData(wrapper.getDataRows());
            log.info("文件解析完成: {}", fileName);
            return tableInfo;
        } catch (Exception e) {
            log.error("文件解析失败: {}", fileName, e);
            throw new BizException("文件解析失败: " + e.getMessage(), e);
        }
    }

    @Data
    @AllArgsConstructor
    private static class Wrapper {

        List<SearchableField> headers;
        List<Map<String, Object>> dataRows;
    }

    private Wrapper parseExcel(String fileExtension, InputStream inputStream) throws Exception {
        // 根据文件扩展名创建相应的Workbook对象
        Workbook workbook = fileExtension.equalsIgnoreCase("xlsx")
            ? new XSSFWorkbook(inputStream) : new HSSFWorkbook(inputStream);
        try (workbook) {
            Sheet sheet = workbook.getSheetAt(0);

            // 获取表头（第一行）
            Row headerRow = sheet.getRow(0);
            List<SearchableField> fields = new ArrayList<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                String header = getCellValueAsString(workbook, cell);

                SearchableField field = new SearchableField();
                field.setZhName(header);
                field.setDescription(header);
                field.setNullable(true);
                field.setType(FieldType.STRING); // 默认类型为字符串

                fields.add(field);
            }

            // 解析数据行（第二行开始）
            List<Map<String, Object>> dataRows = new ArrayList<>();
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row dataRow = sheet.getRow(rowNum);
                Map<String, Object> rowData = new HashMap<>();
                for (int i = 0; i < dataRow.getLastCellNum(); i++) {
                    Cell cell = dataRow.getCell(i);
                    String value = getCellValueAsString(workbook, cell);
                    rowData.put(fields.get(i).getZhName(), value);
                }
                dataRows.add(rowData);
            }
            return new Wrapper(fields, dataRows);
        }
    }

    /**
     * 匹配与表头名称对应的数据模型字段的英文名称
     *
     * @param header 表头名称
     * @param fields 数据模型字段列表
     * @return 字段的英文名称
     */
    private String getFieldEnName(String header, List<DataModelField> fields) {
        for (DataModelField field : fields) {
            if (field.getZhName().equals(header) || field.getEnName().equals(header)) {
                return field.getEnName();
            }
        }
        return null;
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell     单元格
     * @param workbook 工作簿
     * @return 字符串值
     */
    public static String getCellValueAsString(Workbook workbook, Cell cell) {
        if (cell == null) {
            return "";
        }
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().replaceAll("[\\n\\r\\t]", "");
            case NUMERIC -> numeric2String(workbook, cell);
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            default -> "";
        };
    }

    private static String numeric2String(Workbook workbook, Cell cell) {
        if (DateUtil.isCellDateFormatted(cell)) {
            short format = cell.getCellStyle().getDataFormat();
            DataFormat dataFormat = workbook.createDataFormat();
            String formatString = dataFormat.getFormat(format);
            String defaultFormat = createDefaultDateTimeFormat(formatString);
            Date date = cell.getDateCellValue();
            SimpleDateFormat dateFormat = new SimpleDateFormat(defaultFormat);
            return dateFormat.format(date);
        }
        return String.valueOf((long) cell.getNumericCellValue());
    }

    private static String createDefaultDateTimeFormat(String format) {
        if (format == null || format.isEmpty()) {
            return "yyyy-MM-dd HH:mm:ss";
        } else if (isFormatHasDate(format) && isFormatHasTime(format)) {
            return "yyyy-MM-dd HH:mm:ss";
        } else if (isFormatHasDate(format)) {
            return "yyyy-MM-dd";
        } else if (isFormatHasTime(format)) {
            return "HH:mm:ss";
        } else {
            throw new BizException("不支持的日期时间格式: " + format);
        }
    }

    private static boolean isFormatHasDate(String format) {
        return format != null && (format.contains("y") || format.contains("M") || format.contains("d"));
    }

    private static boolean isFormatHasTime(String format) {
        return format != null && (format.contains("H") || format.contains("h") || format.contains("m")
            || format.contains("s"));
    }
}
