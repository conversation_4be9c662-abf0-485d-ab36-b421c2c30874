package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.TaskStatusInconsistency;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.enums.SparkAppStatus;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TaskStatusRecoveryService单元测试
 * 验证单节点同步机制的并发安全性和功能正确性
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@ExtendWith(MockitoExtension.class)
class TaskStatusRecoveryServiceTest {

    @Mock
    private SparkStatusChecker sparkStatusChecker;

    @Mock
    private BatchTaskRecordMapper batchTaskRecordMapper;

    @Mock
    private EnhancedStartupRecovery enhancedStartupRecovery;

    @InjectMocks
    private TaskStatusRecoveryService taskStatusRecoveryService;

    private TaskStatusInconsistency testInconsistency;
    private BatchTaskRecord testTaskRecord;

    @BeforeEach
    void setUp() {
        testInconsistency = new TaskStatusInconsistency();
        testInconsistency.setExecuteId("test-execute-id-001");
        testInconsistency.setDatabaseStatus(BatchTaskStatus.RUNNING);
        testInconsistency.setSparkStatus(SparkAppStatus.SUCCEEDED);
        testInconsistency.setDetectedTime(LocalDateTime.now());

        testTaskRecord = BatchTaskRecord.builder()
                .executeId("test-execute-id-001")
                .taskName("test-task")
                .status(BatchTaskStatus.RUNNING)
                .applicationId("application_123")
                .startTime(LocalDateTime.now().minusMinutes(10))
                .build();
    }

    @Test
    void testFixTaskStatusInconsistency_Success() {
        // 准备测试数据
        when(batchTaskRecordMapper.getByExecuteId("test-execute-id-001")).thenReturn(testTaskRecord);
        when(sparkStatusChecker.checkSingleTaskStatus("test-execute-id-001")).thenReturn(testInconsistency);
        when(batchTaskRecordMapper.updateSelective(any(BatchTaskRecord.class))).thenReturn(1);

        // 执行测试
        boolean result = taskStatusRecoveryService.fixTaskStatusInconsistency(testInconsistency);

        // 验证结果
        assertTrue(result, "任务状态修复应该成功");
        verify(sparkStatusChecker, times(1)).checkSingleTaskStatus("test-execute-id-001");
        verify(batchTaskRecordMapper, times(1)).updateSelective(any(BatchTaskRecord.class));
    }

    @Test
    void testFixTaskStatusInconsistency_NullInconsistency() {
        // 执行测试
        boolean result = taskStatusRecoveryService.fixTaskStatusInconsistency(null);

        // 验证结果
        assertFalse(result, "传入null应该返回false");
        verify(sparkStatusChecker, never()).checkSingleTaskStatus(anyString());
        verify(batchTaskRecordMapper, never()).updateSelective(any());
    }

    @Test
    void testFixTaskStatusInconsistency_TaskRecordNotFound() {
        // 准备测试数据 - 任务记录不存在
        when(batchTaskRecordMapper.getByExecuteId("test-execute-id-001")).thenReturn(null);

        // 执行测试
        boolean result = taskStatusRecoveryService.fixTaskStatusInconsistency(testInconsistency);

        // 验证结果
        assertFalse(result, "任务记录不存在时应该返回false");
        verify(batchTaskRecordMapper, times(1)).getByExecuteId("test-execute-id-001");
        verify(sparkStatusChecker, never()).checkSingleTaskStatus(anyString());
    }

    @Test
    void testFixTaskStatusInconsistency_StatusAlreadyConsistent() {
        // 准备测试数据 - 状态已经一致
        when(batchTaskRecordMapper.getByExecuteId("test-execute-id-001")).thenReturn(testTaskRecord);
        when(sparkStatusChecker.checkSingleTaskStatus("test-execute-id-001")).thenReturn(null);

        // 执行测试
        boolean result = taskStatusRecoveryService.fixTaskStatusInconsistency(testInconsistency);

        // 验证结果
        assertTrue(result, "状态已经一致时应该返回true");
        verify(sparkStatusChecker, times(1)).checkSingleTaskStatus("test-execute-id-001");
        verify(batchTaskRecordMapper, never()).updateSelective(any());
    }

    @Test
    void testConcurrentFixTaskStatusInconsistency() throws InterruptedException {
        // 准备测试数据
        when(batchTaskRecordMapper.getByExecuteId(anyString())).thenReturn(testTaskRecord);
        when(sparkStatusChecker.checkSingleTaskStatus(anyString())).thenReturn(testInconsistency);
        when(batchTaskRecordMapper.updateSelective(any(BatchTaskRecord.class))).thenReturn(1);

        // 并发测试参数
        int threadCount = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        // 创建并发任务
        for (int i = 0; i < threadCount; i++) {
            final int taskId = i;
            executorService.submit(() -> {
                try {
                    startLatch.await(); // 等待统一开始信号

                    TaskStatusInconsistency concurrentInconsistency = new TaskStatusInconsistency();
                    concurrentInconsistency.setExecuteId("concurrent-test-" + taskId);
                    concurrentInconsistency.setDatabaseStatus(BatchTaskStatus.RUNNING);
                    concurrentInconsistency.setSparkStatus(SparkAppStatus.SUCCEEDED);

                    boolean result = taskStatusRecoveryService.fixTaskStatusInconsistency(concurrentInconsistency);
                    if (result) {
                        successCount.incrementAndGet();
                    } else {
                        failureCount.incrementAndGet();
                    }

                } catch (Exception e) {
                    failureCount.incrementAndGet();
                } finally {
                    completeLatch.countDown();
                }
            });
        }

        // 启动所有线程
        startLatch.countDown();

        // 等待所有线程完成
        assertTrue(completeLatch.await(30, TimeUnit.SECONDS), "并发测试应该在30秒内完成");

        // 验证结果
        assertEquals(threadCount, successCount.get() + failureCount.get(), "所有线程都应该执行完成");
        assertTrue(successCount.get() > 0, "至少有一些任务应该成功");

        // 验证方法调用次数
        verify(batchTaskRecordMapper, times(threadCount)).getByExecuteId(anyString());

        executorService.shutdown();
    }

    @Test
    void testGetRecoveryServiceStatus() {
        // 执行测试
        TaskStatusRecoveryService.RecoveryServiceStatus status = taskStatusRecoveryService.getRecoveryServiceStatus();

        // 验证结果
        assertNotNull(status, "状态信息不应该为null");
        assertFalse(status.isRunning(), "初始状态应该不是运行中");
        assertEquals(0, status.getExecutionCount(), "初始执行次数应该为0");
        assertTrue(status.isEnabled(), "恢复功能应该默认启用");
    }

    @Test
    void testRecoveryServiceStatus_ToString() {
        // 创建状态对象
        TaskStatusRecoveryService.RecoveryServiceStatus status = 
                new TaskStatusRecoveryService.RecoveryServiceStatus(false, 5, 1234567890L, true);

        // 执行测试
        String statusString = status.toString();

        // 验证结果
        assertNotNull(statusString, "toString结果不应该为null");
        assertTrue(statusString.contains("running=false"), "应该包含运行状态");
        assertTrue(statusString.contains("execCount=5"), "应该包含执行次数");
        assertTrue(statusString.contains("lastExecTime=1234567890"), "应该包含最后执行时间");
        assertTrue(statusString.contains("enabled=true"), "应该包含启用状态");
    }

    @Test
    void testFixTaskStatusInconsistency_DatabaseUpdateFailure() {
        // 准备测试数据 - 数据库更新失败
        when(batchTaskRecordMapper.getByExecuteId("test-execute-id-001")).thenReturn(testTaskRecord);
        when(sparkStatusChecker.checkSingleTaskStatus("test-execute-id-001")).thenReturn(testInconsistency);
        when(batchTaskRecordMapper.updateSelective(any(BatchTaskRecord.class))).thenReturn(0);

        // 执行测试
        boolean result = taskStatusRecoveryService.fixTaskStatusInconsistency(testInconsistency);

        // 验证结果
        assertFalse(result, "数据库更新失败时应该返回false");
        verify(batchTaskRecordMapper, times(1)).updateSelective(any(BatchTaskRecord.class));
    }

    @Test
    void testFixTaskStatusInconsistency_SparkStatusCheckerNotAvailable() {
        // 创建没有SparkStatusChecker的服务实例
        TaskStatusRecoveryService serviceWithoutChecker = new TaskStatusRecoveryService();
        // 通过反射设置mapper（简化测试）
        try {
            java.lang.reflect.Field mapperField = TaskStatusRecoveryService.class.getDeclaredField("batchTaskRecordMapper");
            mapperField.setAccessible(true);
            mapperField.set(serviceWithoutChecker, batchTaskRecordMapper);
        } catch (Exception e) {
            fail("反射设置失败: " + e.getMessage());
        }

        // 准备测试数据
        when(batchTaskRecordMapper.getByExecuteId("test-execute-id-001")).thenReturn(testTaskRecord);

        // 执行测试
        boolean result = serviceWithoutChecker.fixTaskStatusInconsistency(testInconsistency);

        // 验证结果
        assertFalse(result, "SparkStatusChecker不可用时应该返回false");
    }

    @Test
    void testAsyncConcurrentAccess() throws Exception {
        // 准备测试数据
        when(batchTaskRecordMapper.getByExecuteId(anyString())).thenReturn(testTaskRecord);
        when(sparkStatusChecker.checkSingleTaskStatus(anyString())).thenReturn(testInconsistency);
        when(batchTaskRecordMapper.updateSelective(any(BatchTaskRecord.class))).thenReturn(1);

        // 异步并发测试
        int asyncTaskCount = 5;
        CompletableFuture<Boolean>[] futures = new CompletableFuture[asyncTaskCount];

        for (int i = 0; i < asyncTaskCount; i++) {
            final int taskId = i;
            futures[i] = CompletableFuture.supplyAsync(() -> {
                TaskStatusInconsistency asyncInconsistency = new TaskStatusInconsistency();
                asyncInconsistency.setExecuteId("async-test-" + taskId);
                asyncInconsistency.setDatabaseStatus(BatchTaskStatus.RUNNING);
                asyncInconsistency.setSparkStatus(SparkAppStatus.SUCCEEDED);
                
                return taskStatusRecoveryService.fixTaskStatusInconsistency(asyncInconsistency);
            });
        }

        // 等待所有异步任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures);
        allTasks.get(30, TimeUnit.SECONDS);

        // 验证结果
        for (CompletableFuture<Boolean> future : futures) {
            assertTrue(future.get(), "所有异步任务都应该成功");
        }
    }
}