package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.TaskStatusInconsistency;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.enums.SparkAppStatus;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 任务状态恢复服务 - 单节点版本
 * 负责启动时自动检测和修复任务状态不一致问题，确保数据库状态与Spark集群状态保持同步
 * 简化版本适用于单节点部署，使用本地同步机制而非分布式锁
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Slf4j
@Service
public class TaskStatusRecoveryService {

    // 单节点同步控制变量
    private static final AtomicBoolean STARTUP_RECOVERY_RUNNING = new AtomicBoolean(false);
    private static final Object RECOVERY_LOCK = new Object();
    private static final AtomicLong RECOVERY_EXECUTION_COUNT = new AtomicLong(0);
    private static final AtomicLong LAST_RECOVERY_TIME = new AtomicLong(0);
    
    // 防止频繁执行的最小间隔（毫秒）
    private static final long MIN_RECOVERY_INTERVAL_MS = 30_000;

    @Autowired(required = false)
    private SparkStatusChecker sparkStatusChecker;

    @Autowired
    private BatchTaskRecordMapper batchTaskRecordMapper;

    @Autowired(required = false)
    private EnhancedStartupRecovery enhancedStartupRecovery;

    @Value("${moye.batch.status.recovery.enabled:true}")
    private boolean recoveryEnabled;

    /**
     * 应用启动完成后自动执行状态恢复 - 单节点版本
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        if (!recoveryEnabled) {
            log.info("任务状态恢复功能已禁用，跳过启动时状态恢复");
            return;
        }

        log.info("应用启动完成，开始执行任务状态恢复");
        
        // 单节点部署，使用增强的本地同步机制防止重复和频繁执行
        long currentTime = System.currentTimeMillis();
        long lastExecutionTime = LAST_RECOVERY_TIME.get();
        
        if (currentTime - lastExecutionTime < MIN_RECOVERY_INTERVAL_MS) {
            log.info("距离上次恢复执行时间过短（{}ms），跳过本次执行", currentTime - lastExecutionTime);
            return;
        }
        
        if (STARTUP_RECOVERY_RUNNING.compareAndSet(false, true)) {
            try {
                long executionId = RECOVERY_EXECUTION_COUNT.incrementAndGet();
                LAST_RECOVERY_TIME.set(currentTime);
                log.info("开始执行第{}次状态恢复", executionId);
                
                recoverTaskStatusOnStartup();
                
                log.info("完成第{}次状态恢复", executionId);
            } finally {
                STARTUP_RECOVERY_RUNNING.set(false);
            }
        } else {
            log.info("启动恢复正在执行中（执行次数: {}），跳过重复执行", RECOVERY_EXECUTION_COUNT.get());
        }
    }

    /**
     * 服务启动时恢复任务状态
     */
    private void recoverTaskStatusOnStartup() {
        log.info("开始执行服务启动时的任务状态恢复");
        long startTime = System.currentTimeMillis();
        
        try {
            if (enhancedStartupRecovery != null) {
                log.debug("调用增强启动恢复服务执行状态恢复");
                EnhancedStartupRecovery.RecoveryStatistics statistics = enhancedStartupRecovery.performStartupRecovery();
                long duration = System.currentTimeMillis() - startTime;
                
                if (statistics.isSuccessful()) {
                    log.info("启动恢复成功完成，耗时: {}ms, 详情: {}", duration, statistics.getSummary());
                } else {
                    log.warn("启动恢复完成但有警告，耗时: {}ms, 详情: {}", duration, statistics.getSummary());
                    
                    // 记录具体的错误信息
                    if (!statistics.getErrors().isEmpty()) {
                        log.warn("启动恢复过程中的错误详情:");
                        statistics.getErrors().forEach(error -> log.warn("  - {}", error));
                    }
                }
                
                // 记录关键指标
                if (statistics.hasInconsistencies()) {
                    log.info("状态不一致处理结果: 发现{}个, 修复{}个, 失败{}个", 
                            statistics.getInconsistencyCount(), 
                            statistics.getFixedCount(), 
                            statistics.getFailedCount());
                }
                
            } else {
                log.warn("EnhancedStartupRecovery服务不可用，跳过启动恢复");
            }
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("服务启动时状态恢复发生异常，耗时: {}ms", duration, e);
            
            // 尝试基本的错误恢复
            try {
                log.info("尝试执行基本错误恢复策略");
                performBasicErrorRecovery();
            } catch (Exception recoveryError) {
                log.error("基本错误恢复也失败了", recoveryError);
            }
        }
    }

    /**
     * 执行基本的错误恢复策略
     * 当主要恢复流程失败时使用
     */
    private void performBasicErrorRecovery() {
        log.debug("执行基本错误恢复: 检查服务依赖状态");
        
        // 检查关键依赖
        boolean sparkCheckerAvailable = sparkStatusChecker != null;
        boolean mapperAvailable = batchTaskRecordMapper != null;
        
        log.info("基本错误恢复 - 依赖状态检查: SparkChecker={}, Mapper={}", 
                sparkCheckerAvailable, mapperAvailable);
        
        if (!sparkCheckerAvailable) {
            log.warn("SparkStatusChecker不可用，无法执行状态检查");
        }
        
        if (!mapperAvailable) {
            log.error("BatchTaskRecordMapper不可用，数据库操作将失败");
        }
    }

    /**
     * 修复单个任务的状态不一致问题 - 单节点版本
     * 供其他组件内部调用，使用增强的本地同步锁机制
     *
     * @param inconsistency 状态不一致记录
     * @return 是否修复成功
     */
    public boolean fixTaskStatusInconsistency(TaskStatusInconsistency inconsistency) {
        if (inconsistency == null) {
            log.warn("状态不一致记录为null，无法执行修复");
            return false;
        }
        
        // 生成随机延迟，避免高并发时的锁竞争
        long randomDelay = ThreadLocalRandom.current().nextLong(10, 100);
        try {
            Thread.sleep(randomDelay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待随机延迟时被中断，执行ID: {}", inconsistency.getExecuteId());
        }

        // 单节点部署，使用synchronized本地锁，增加超时检测
        long lockStartTime = System.currentTimeMillis();
        synchronized (RECOVERY_LOCK) {
            long lockAcquiredTime = System.currentTimeMillis();
            long lockWaitTime = lockAcquiredTime - lockStartTime;
            
            if (lockWaitTime > 5000) { // 等待锁超过5秒则记录警告
                log.warn("获取修复锁等待时间较长: {}ms，执行ID: {}", lockWaitTime, inconsistency.getExecuteId());
            }
            
            try {
                return doFixTaskStatus(inconsistency);
            } catch (Exception e) {
                log.error("修复任务状态不一致时发生异常，执行ID: " + inconsistency.getExecuteId(), e);
                inconsistency.markAsFailed(e.getMessage());
                return false;
            } finally {
                long lockHoldTime = System.currentTimeMillis() - lockAcquiredTime;
                if (lockHoldTime > 10000) { // 持有锁超过10秒则记录警告
                    log.warn("持有修复锁时间较长: {}ms，执行ID: {}", lockHoldTime, inconsistency.getExecuteId());
                }
            }
        }
    }

    /**
     * 执行具体的状态修复逻辑
     *
     * @param inconsistency 状态不一致记录
     * @return 是否修复成功
     */
    private boolean doFixTaskStatus(TaskStatusInconsistency inconsistency) {
        inconsistency.markAsFixing();

        try {
            // 重新查询最新的数据库记录和Spark状态，确保信息是最新的
            BatchTaskRecord currentRecord = batchTaskRecordMapper.getByExecuteId(inconsistency.getExecuteId());
            if (currentRecord == null) {
                log.warn("任务记录不存在，无法修复，执行ID: {}", inconsistency.getExecuteId());
                inconsistency.markAsFailed("任务记录不存在");
                return false;
            }

            // 再次确认状态不一致（避免在获取锁期间状态已经被其他线程修复）
            if (sparkStatusChecker != null) {
                TaskStatusInconsistency currentInconsistency = sparkStatusChecker.checkSingleTaskStatus(inconsistency.getExecuteId());
                if (currentInconsistency == null) {
                    log.info("任务状态已经一致，无需修复，执行ID: {}", inconsistency.getExecuteId());
                    inconsistency.markAsFixed();
                    return true;
                }

                // 根据Spark状态确定应该修复为什么状态
                BatchTaskStatus targetStatus = determineTargetStatus(currentInconsistency);
                if (targetStatus == null) {
                    log.warn("无法确定目标状态，跳过修复，执行ID: {}", inconsistency.getExecuteId());
                    inconsistency.markAsFailed("无法确定目标状态");
                    return false;
                }

                // 执行状态更新
                boolean updateSuccess = updateTaskStatus(currentRecord, targetStatus);
                if (updateSuccess) {
                    log.info("成功修复任务状态不一致，执行ID: {}, 原状态: {}, 新状态: {}",
                            inconsistency.getExecuteId(), inconsistency.getDatabaseStatus(), targetStatus);
                    inconsistency.markAsFixed();
                    return true;
                } else {
                    log.error("更新任务状态失败，执行ID: {}", inconsistency.getExecuteId());
                    inconsistency.markAsFailed("数据库更新失败");
                    return false;
                }
            } else {
                log.warn("SparkStatusChecker服务不可用，跳过状态修复，执行ID: {}", inconsistency.getExecuteId());
                inconsistency.markAsFailed("SparkStatusChecker服务不可用");
                return false;
            }

        } catch (Exception e) {
            log.error("执行状态修复时发生异常，执行ID: " + inconsistency.getExecuteId(), e);
            inconsistency.markAsFailed(e.getMessage());
            return false;
        }
    }

    /**
     * 根据Spark状态确定目标状态
     *
     * @param inconsistency 状态不一致记录
     * @return 目标状态
     */
    private BatchTaskStatus determineTargetStatus(TaskStatusInconsistency inconsistency) {
        SparkAppStatus sparkStatus = inconsistency.getSparkStatus();
        
        if (sparkStatus == null || sparkStatus == SparkAppStatus.UNKNOWN) {
            // 如果Spark状态未知，且数据库状态为RUNNING，可能是服务异常导致
            // 保守处理：标记为FAILED
            return BatchTaskStatus.FAILED;
        }

        // 根据Spark状态映射到批处理任务状态
        switch (sparkStatus) {
            case SUCCEEDED:
                return BatchTaskStatus.SUCCESS;
            case FAILED:
                return BatchTaskStatus.FAILED;
            case KILLED:
                return BatchTaskStatus.KILLED;
            case NEW:
            case RUNNING:
                // Spark仍在运行，数据库状态可能不对，但这种情况应该保持RUNNING
                return BatchTaskStatus.RUNNING;
            default:
                return null;
        }
    }

    /**
     * 更新任务状态
     *
     * @param record      任务记录
     * @param targetStatus 目标状态
     * @return 是否更新成功
     */
    private boolean updateTaskStatus(BatchTaskRecord record, BatchTaskStatus targetStatus) {
        try {
            BatchTaskRecord updateRecord = BatchTaskRecord.builder()
                    .executeId(record.getExecuteId())
                    .status(targetStatus)
                    .build();

            // 如果是终止状态，设置结束时间
            if (targetStatus.isFinished()) {
                updateRecord.setEndTime(LocalDateTime.now());
                updateRecord.setIsError(targetStatus == BatchTaskStatus.SUCCESS
                        ? BatchTaskRecord.NON_ERROR : BatchTaskRecord.IS_ERROR);
            }

            int updateCount = batchTaskRecordMapper.updateSelective(updateRecord);
            return updateCount > 0;

        } catch (Exception e) {
            log.error("更新任务状态时发生异常，执行ID: " + record.getExecuteId(), e);
            return false;
        }
    }

    /**
     * 获取当前恢复服务的状态信息 - 单节点同步监控
     *
     * @return 恢复服务状态信息
     */
    public RecoveryServiceStatus getRecoveryServiceStatus() {
        return new RecoveryServiceStatus(
                STARTUP_RECOVERY_RUNNING.get(),
                RECOVERY_EXECUTION_COUNT.get(),
                LAST_RECOVERY_TIME.get(),
                recoveryEnabled
        );
    }

    /**
     * 恢复服务状态信息类
     */
    public static class RecoveryServiceStatus {
        private final boolean isRunning;
        private final long executionCount;
        private final long lastExecutionTime;
        private final boolean enabled;

        public RecoveryServiceStatus(boolean isRunning, long executionCount, long lastExecutionTime, boolean enabled) {
            this.isRunning = isRunning;
            this.executionCount = executionCount;
            this.lastExecutionTime = lastExecutionTime;
            this.enabled = enabled;
        }

        public boolean isRunning() { return isRunning; }
        public long getExecutionCount() { return executionCount; }
        public long getLastExecutionTime() { return lastExecutionTime; }
        public boolean isEnabled() { return enabled; }

        @Override
        public String toString() {
            return String.format("RecoveryServiceStatus{running=%s, execCount=%d, lastExecTime=%d, enabled=%s}",
                    isRunning, executionCount, lastExecutionTime, enabled);
        }
    }
}