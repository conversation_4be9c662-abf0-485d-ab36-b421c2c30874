package com.trs.moye.batch.engine.enums;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 批处理任务状态枚举
 * 增强版本，支持更丰富的状态管理和转换功能
 *
 * <AUTHOR> (原作者)
 * <AUTHOR> (增强功能)
 * @since 2025/7/28 17:20
 */
public enum BatchTaskStatus {
    /**
     * 等待中
     */
    WAITING("等待中", StatusCategory.PENDING, 1),
    /**
     * 运行中
     */
    RUNNING("运行中", StatusCategory.ACTIVE, 2),
    /**
     * 成功
     */
    SUCCESS("成功", StatusCategory.COMPLETED, 3),
    /**
     * 失败
     */
    FAILED("失败", StatusCategory.FAILED, 4),
    /**
     * 被丢弃
     */
    DISCARDED("被丢弃", StatusCategory.TERMINATED, 5),
    /**
     * 被终止
     */
    KILLED("被终止", StatusCategory.TERMINATED, 6);

    private final String description;
    private final StatusCategory category;
    private final int priority;

    /**
     * 状态类别枚举
     */
    public enum StatusCategory {
        /**
         * 等待中
         */
        PENDING,
        /**
         * 活跃中
         */
        ACTIVE,
        /**
         * 成功完成
         */
        COMPLETED,
        /**
         * 失败
         */
        FAILED,
        /**
         * 被终止
         */
        TERMINATED
    }

    BatchTaskStatus(String description, StatusCategory category, int priority) {
        this.description = description;
        this.category = category;
        this.priority = priority;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取状态类别
     *
     * @return 状态类别
     */
    public StatusCategory getCategory() {
        return category;
    }

    /**
     * 获取状态优先级
     *
     * @return 优先级（数字越小优先级越高）
     */
    public int getPriority() {
        return priority;
    }

    /**
     * 判断是否为已结束状态
     *
     * @return 是否已结束
     * <AUTHOR>
     */
    public boolean isFinished() {
        return category == StatusCategory.COMPLETED || 
               category == StatusCategory.FAILED || 
               category == StatusCategory.TERMINATED;
    }

    /**
     * 判断是否为活跃状态(未结束)
     *
     * @return 是否活跃
     * <AUTHOR>
     */
    public boolean isActive() {
        return category == StatusCategory.PENDING || 
               category == StatusCategory.ACTIVE;
    }

    /**
     * 判断是否为成功状态
     *
     * @return 是否成功
     * <AUTHOR>
     */
    public boolean isSuccessful() {
        return category == StatusCategory.COMPLETED;
    }

    /**
     * 判断是否为失败状态
     *
     * @return 是否失败
     * <AUTHOR>
     */
    public boolean isFailed() {
        return category == StatusCategory.FAILED || 
               category == StatusCategory.TERMINATED;
    }

    /**
     * 根据状态类别获取所有状态
     *
     * @param category 状态类别
     * @return 该类别下的所有状态
     * <AUTHOR>
     */
    public static Set<BatchTaskStatus> getByCategory(StatusCategory category) {
        return Arrays.stream(values())
                .filter(status -> status.category == category)
                .collect(Collectors.toSet());
    }

    /**
     * 获取所有终止状态
     *
     * @return 终止状态集合
     * <AUTHOR>
     */
    public static Set<BatchTaskStatus> getFinishedStatuses() {
        return Arrays.stream(values())
                .filter(BatchTaskStatus::isFinished)
                .collect(Collectors.toSet());
    }

    /**
     * 获取所有活跃状态
     *
     * @return 活跃状态集合
     * <AUTHOR>
     */
    public static Set<BatchTaskStatus> getActiveStatuses() {
        return Arrays.stream(values())
                .filter(BatchTaskStatus::isActive)
                .collect(Collectors.toSet());
    }

    /**
     * 根据描述获取状态
     *
     * @param description 状态描述
     * @return 匹配的状态，没有匹配的返回null
     * <AUTHOR>
     */
    public static BatchTaskStatus fromDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        
        String normalized = description.trim();
        for (BatchTaskStatus status : values()) {
            if (status.description.equals(normalized) || status.name().equalsIgnoreCase(normalized)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断状态是否可以转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     * <AUTHOR>
     */
    public boolean canTransitionTo(BatchTaskStatus targetStatus) {
        // 已完成的状态不能转换
        if (isFinished()) {
            return false;
        }
        
        switch (this) {
            case WAITING:
                return targetStatus == RUNNING || targetStatus == FAILED || 
                       targetStatus == DISCARDED || targetStatus == KILLED;
            case RUNNING:
                return targetStatus == SUCCESS || targetStatus == FAILED || 
                       targetStatus == KILLED;
            default:
                return false;
        }
    }

    /**
     * 获取状态的严重程度
     * 用于状态不一致分析
     *
     * @return 严重程度评分（0-10，10最严重）
     * <AUTHOR>
     */
    public int getSeverityScore() {
        switch (this) {
            case SUCCESS:
                return 0;
            case WAITING:
                return 2;
            case RUNNING:
                return 3;
            case FAILED:
                return 7;
            case DISCARDED:
                return 6;
            case KILLED:
                return 8;
            default:
                return 5;
        }
    }

    /**
     * 获取状态的显示颜色（用于UI展示）
     *
     * @return CSS颜色类名
     * <AUTHOR>
     */
    public String getDisplayColor() {
        switch (category) {
            case COMPLETED:
                return "text-success";
            case FAILED:
            case TERMINATED:
                return "text-danger";
            case ACTIVE:
                return "text-primary";
            case PENDING:
                return "text-warning";
            default:
                return "text-muted";
        }
    }

    /**
     * 获取状态的详细描述
     *
     * @return 详细描述
     * <AUTHOR>
     */
    public String getDetailedDescription() {
        switch (this) {
            case WAITING:
                return "任务已提交，等待调度执行";
            case RUNNING:
                return "任务正在执行中";
            case SUCCESS:
                return "任务执行成功完成";
            case FAILED:
                return "任务执行失败";
            case DISCARDED:
                return "任务被系统丢弃";
            case KILLED:
                return "任务被用户或系统终止";
            default:
                return description;
        }
    }

    /**
     * 获取与SparkAppStatus的映射关系
     *
     * @return 对应的SparkAppStatus，如果无法映射返回null
     * <AUTHOR>
     */
    public SparkAppStatus toSparkAppStatus() {
        switch (this) {
            case WAITING:
                return SparkAppStatus.NEW;
            case RUNNING:
                return SparkAppStatus.RUNNING;
            case SUCCESS:
                return SparkAppStatus.SUCCEEDED;
            case FAILED:
                return SparkAppStatus.FAILED;
            case KILLED:
            case DISCARDED:
                return SparkAppStatus.KILLED;
            default:
                return SparkAppStatus.UNKNOWN;
        }
    }

    /**
     * 判断与SparkAppStatus是否一致
     *
     * @param sparkStatus Spark状态
     * @return 是否一致
     * <AUTHOR>
     */
    public boolean isConsistentWith(SparkAppStatus sparkStatus) {
        if (sparkStatus == null) {
            return false;
        }

        switch (this) {
            case WAITING:
                return sparkStatus.isActive() || sparkStatus == SparkAppStatus.NEW;
            case RUNNING:
                return sparkStatus.isActive();
            case SUCCESS:
                return sparkStatus == SparkAppStatus.SUCCEEDED;
            case FAILED:
                return sparkStatus == SparkAppStatus.FAILED;
            case KILLED:
            case DISCARDED:
                return sparkStatus == SparkAppStatus.KILLED || sparkStatus.isFinished();
            default:
                return false;
        }
    }

    /**
     * 比较状态优先级
     *
     * @param other 另一个状态
     * @return 优先级比较结果
     * <AUTHOR>
     */
    public int comparePriority(BatchTaskStatus other) {
        return Integer.compare(this.priority, other.priority);
    }

    /**
     * 获取状态的完整信息
     *
     * @return 状态信息字符串
     * <AUTHOR>
     */
    @Override
    public String toString() {
        return String.format("BatchTaskStatus{name='%s', description='%s', category=%s, priority=%d, severityScore=%d}", 
                name(), description, category, priority, getSeverityScore());
    }
}
