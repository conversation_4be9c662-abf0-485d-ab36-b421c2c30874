package com.trs.moye.batch.engine.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.enums.SparkAppStatus;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Spark REST API客户端
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Slf4j
@Component
public class SparkRestApiClient {

    private static final String APPLICATIONS_API = "/api/v1/applications";
    private static final String APPLICATION_DETAIL_API = "/api/v1/applications/{applicationId}";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    @Value("${moye.batch.spark.history-server.url:http://localhost:18080}")
    private String sparkHistoryServerUrl;

    @Value("${moye.batch.spark.rest-api.timeout:30000}")
    private int apiTimeout;

    @Value("${moye.batch.spark.rest-api.retry-count:3}")
    private int retryCount;

    @Autowired
    private OkHttpClient okHttpClient;

    /**
     * 获取单个应用信息
     *
     * @param applicationId 应用ID
     * @return SparkApplicationInfo
     */
    public SparkApplicationInfo getApplicationInfo(String applicationId) {
        if (applicationId == null || applicationId.trim().isEmpty()) {
            log.warn("应用ID为空，无法查询Spark应用状态");
            return SparkApplicationInfo.unknownStatus(applicationId);
        }

        String url = sparkHistoryServerUrl + APPLICATION_DETAIL_API.replace("{applicationId}", applicationId);

        for (int attempt = 1; attempt <= retryCount; attempt++) {
            try {
                log.debug("查询Spark应用状态，应用ID: {}, URL: {}, 第{}次尝试", applicationId, url, attempt);

                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

                try (Response response = okHttpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        String responseBody = Objects.requireNonNull(response.body()).string();
                        return parseApplicationInfo(responseBody, applicationId);
                    } else if (response.code() == 404) {
                        log.warn("应用{}未在Spark History Server中找到，可能仍在运行或已被清理", applicationId);
                        return checkRunningApplication(applicationId);
                    } else {
                        log.warn("查询Spark应用状态失败，应用ID: {}, 状态码: {}, 响应: {}",
                            applicationId, response.code(), response.message());
                        if (attempt == retryCount) {
                            return SparkApplicationInfo.unknownStatus(applicationId);
                        }
                    }
                }
            } catch (IOException e) {
                log.warn("查询Spark应用状态发生IO异常，应用ID: {}, 第{}次尝试失败: {}",
                    applicationId, attempt, e.getMessage());
                if (attempt == retryCount) {
                    log.error("查询Spark应用状态最终失败，应用ID: " + applicationId, e);
                    return SparkApplicationInfo.unknownStatus(applicationId);
                }

                // 指数退避重试
                try {
                    TimeUnit.MILLISECONDS.sleep(Math.min(1000 * (long) Math.pow(2, attempt - 1), 10000));
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return SparkApplicationInfo.unknownStatus(applicationId);
                }
            } catch (Exception e) {
                log.error("查询Spark应用状态发生未知异常，应用ID: " + applicationId, e);
                return SparkApplicationInfo.unknownStatus(applicationId);
            }
        }

        return SparkApplicationInfo.unknownStatus(applicationId);
    }

    /**
     * 批量获取应用信息
     *
     * @param applicationIds 应用ID列表
     * @return 应用ID到状态信息的映射
     */
    public Map<String, SparkApplicationInfo> batchGetApplicationInfo(Iterable<String> applicationIds) {
        Map<String, SparkApplicationInfo> results = new HashMap<>();

        for (String applicationId : applicationIds) {
            try {
                SparkApplicationInfo info = getApplicationInfo(applicationId);
                results.put(applicationId, info);
            } catch (Exception e) {
                log.error("批量查询应用状态时发生异常，应用ID: " + applicationId, e);
                results.put(applicationId, SparkApplicationInfo.unknownStatus(applicationId));
            }
        }

        return results;
    }

    /**
     * 检查正在运行的应用(通过Spark Master API)
     *
     * @param applicationId 应用ID
     * @return SparkApplicationInfo
     */
    private SparkApplicationInfo checkRunningApplication(String applicationId) {
        // 如果History Server中没有找到，可能应用仍在运行
        // 这里可以尝试通过Spark Master API或YARN API查询
        // 目前先返回运行中状态，后续可以扩展
        log.debug("应用{}未在History Server中找到，假设仍在运行", applicationId);
        return SparkApplicationInfo.builder()
            .applicationId(applicationId)
            .status(SparkAppStatus.RUNNING)
            .lastUpdateTime(LocalDateTime.now())
            .build();
    }

    /**
     * 解析应用信息响应
     *
     * @param responseBody  响应体
     * @param applicationId 应用ID
     * @return SparkApplicationInfo
     */
    private SparkApplicationInfo parseApplicationInfo(String responseBody, String applicationId) {
        try {
            JsonNode rootNode = JsonUtils.OBJECT_MAPPER.readTree(responseBody);

            String status = rootNode.path("attempts").get(0).path("completed").asBoolean()
                ? "SUCCEEDED" : "RUNNING";

            // 检查是否有失败标记
            if (rootNode.path("attempts").get(0).has("endTime")) {
                JsonNode attempt = rootNode.path("attempts").get(0);
                boolean completed = attempt.path("completed").asBoolean();
                if (completed) {
                    // 检查是否有异常结束
                    String endTimeStr = attempt.path("endTime").asText();
                    String startTimeStr = attempt.path("startTime").asText();
                    if (endTimeStr != null && !endTimeStr.isEmpty()) {
                        // 可以通过其他字段判断是成功还是失败，这里简化处理
                        status = "SUCCEEDED";
                    }
                }
            }

            SparkAppStatus sparkStatus = SparkAppStatus.fromCode(status);

            SparkApplicationInfo.SparkApplicationInfoBuilder builder = SparkApplicationInfo.builder()
                .applicationId(applicationId)
                .applicationName(rootNode.path("name").asText())
                .status(sparkStatus)
                .lastUpdateTime(LocalDateTime.now());

            // 解析时间字段
            JsonNode attempts = rootNode.path("attempts");
            if (attempts.isArray() && attempts.size() > 0) {
                JsonNode firstAttempt = attempts.get(0);

                String startTimeStr = firstAttempt.path("startTime").asText();
                if (!startTimeStr.isEmpty()) {
                    try {
                        builder.startTime(parseDateTime(startTimeStr));
                    } catch (Exception e) {
                        log.debug("解析开始时间失败: {}", startTimeStr);
                    }
                }

                String endTimeStr = firstAttempt.path("endTime").asText();
                if (!endTimeStr.isEmpty()) {
                    try {
                        builder.endTime(parseDateTime(endTimeStr));
                    } catch (Exception e) {
                        log.debug("解析结束时间失败: {}", endTimeStr);
                    }
                }

                long duration = firstAttempt.path("duration").asLong(0);
                if (duration > 0) {
                    builder.duration(duration);
                }
            }

            return builder.build();

        } catch (Exception e) {
            log.error("解析Spark应用信息失败，应用ID: " + applicationId, e);
            return SparkApplicationInfo.unknownStatus(applicationId);
        }
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        try {
            // Spark API返回的是ISO格式的时间戳
            if (dateTimeStr.contains("T")) {
                return LocalDateTime.parse(dateTimeStr.substring(0, 19));
            } else {
                // 如果是时间戳格式
                long timestamp = Long.parseLong(dateTimeStr);
                return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, java.time.ZoneOffset.UTC);
            }
        } catch (Exception e) {
            log.debug("解析时间字符串失败: {}", dateTimeStr);
            throw new RuntimeException("无法解析时间: " + dateTimeStr, e);
        }
    }

    /**
     * 测试Spark History Server连接
     *
     * @return 是否连接成功
     */
    public boolean testConnection() {
        String url = sparkHistoryServerUrl + APPLICATIONS_API + "?limit=1";

        try {
            Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                boolean success = response.isSuccessful();
                log.info("Spark History Server连接测试结果: {}, URL: {}", success ? "成功" : "失败", url);
                return success;
            }
        } catch (Exception e) {
            log.error("测试Spark History Server连接失败，URL: " + url, e);
            return false;
        }
    }
}