package com.trs.moye.batch.engine.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Spark应用状态枚举
 * 增强版本，支持更丰富的状态分类和转换功能
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Getter
@AllArgsConstructor
public enum SparkAppStatus {

    /**
     * 新建
     */
    NEW("NEW", "新建", StatusCategory.PENDING),

    /**
     * 运行中
     */
    RUNNING("RUNNING", "运行中", StatusCategory.ACTIVE),

    /**
     * 成功完成
     */
    SUCCEEDED("SUCCEEDED", "成功完成", StatusCategory.SUCCESS),

    /**
     * 失败
     */
    FAILED("FAILED", "失败", StatusCategory.FAILED),

    /**
     * 被终止
     */
    KILLED("KILLED", "被终止", StatusCategory.TERMINATED),

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "未知状态", StatusCategory.UNKNOWN);

    private final String code;
    private final String description;
    private final StatusCategory category;

    /**
     * 状态类别枚举
     */
    public enum StatusCategory {
        /**
         * 等待中
         */
        PENDING,
        /**
         * 活跃中
         */
        ACTIVE,
        /**
         * 成功完成
         */
        SUCCESS,
        /**
         * 失败
         */
        FAILED,
        /**
         * 被终止
         */
        TERMINATED,
        /**
         * 未知
         */
        UNKNOWN
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return SparkAppStatus枚举值
     */
    public static SparkAppStatus fromCode(String code) {
        if (code == null) {
            return UNKNOWN;
        }
        
        String normalizedCode = code.trim().toUpperCase();
        
        for (SparkAppStatus status : values()) {
            if (status.getCode().equals(normalizedCode)) {
                return status;
            }
        }
        
        // 尝试模糊匹配常见的状态码变体
        return fromCodeFuzzyMatch(normalizedCode);
    }

    /**
     * 模糊匹配状态码
     *
     * @param code 状态码
     * @return SparkAppStatus枚举值
     * <AUTHOR>
     */
    private static SparkAppStatus fromCodeFuzzyMatch(String code) {
        switch (code) {
            case "SUBMITTED":
            case "ACCEPTED":
            case "PENDING":
                return NEW;
            case "EXECUTING":
            case "ACTIVE":
                return RUNNING;
            case "FINISHED":
            case "COMPLETED":
            case "SUCCESS":
                return SUCCEEDED;
            case "ERROR":
            case "FAILURE":
                return FAILED;
            case "CANCELLED":
            case "TERMINATED":
                return KILLED;
            default:
                return UNKNOWN;
        }
    }

    /**
     * 根据状态类别获取所有状态
     *
     * @param category 状态类别
     * @return 该类别下的所有状态
     * <AUTHOR>
     */
    public static Set<SparkAppStatus> getByCategory(StatusCategory category) {
        return Arrays.stream(values())
                .filter(status -> status.category == category)
                .collect(Collectors.toSet());
    }

    /**
     * 获取所有终止状态
     *
     * @return 终止状态集合
     * <AUTHOR>
     */
    public static Set<SparkAppStatus> getFinishedStatuses() {
        return Arrays.stream(values())
                .filter(SparkAppStatus::isFinished)
                .collect(Collectors.toSet());
    }

    /**
     * 获取所有活跃状态
     *
     * @return 活跃状态集合
     * <AUTHOR>
     */
    public static Set<SparkAppStatus> getActiveStatuses() {
        return Arrays.stream(values())
                .filter(SparkAppStatus::isActive)
                .collect(Collectors.toSet());
    }

    /**
     * 判断是否为终止状态
     *
     * @return 是否终止
     */
    public boolean isFinished() {
        return category == StatusCategory.SUCCESS || 
               category == StatusCategory.FAILED || 
               category == StatusCategory.TERMINATED;
    }

    /**
     * 判断是否为活跃状态
     *
     * @return 是否活跃
     */
    public boolean isActive() {
        return category == StatusCategory.PENDING || 
               category == StatusCategory.ACTIVE;
    }

    /**
     * 判断是否为成功状态
     *
     * @return 是否成功
     * <AUTHOR>
     */
    public boolean isSuccessful() {
        return category == StatusCategory.SUCCESS;
    }

    /**
     * 判断是否为失败状态
     *
     * @return 是否失败
     * <AUTHOR>
     */
    public boolean isFailed() {
        return category == StatusCategory.FAILED || 
               category == StatusCategory.TERMINATED;
    }

    /**
     * 判断是否为未知状态
     *
     * @return 是否未知
     * <AUTHOR>
     */
    public boolean isUnknown() {
        return category == StatusCategory.UNKNOWN;
    }

    /**
     * 获取状态的严重程度
     * 用于状态不一致分析
     *
     * @return 严重程度评分（0-10，10最严重）
     * <AUTHOR>
     */
    public int getSeverityScore() {
        switch (this) {
            case SUCCEEDED:
                return 0;
            case NEW:
                return 2;
            case RUNNING:
                return 3;
            case FAILED:
                return 7;
            case KILLED:
                return 8;
            case UNKNOWN:
                return 9;
            default:
                return 5;
        }
    }

    /**
     * 判断状态是否可以转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     * <AUTHOR>
     */
    public boolean canTransitionTo(SparkAppStatus targetStatus) {
        // 已完成的状态不能转换
        if (isFinished()) {
            return false;
        }
        
        // 未知状态可以转换到任何状态
        if (this == UNKNOWN) {
            return true;
        }
        
        switch (this) {
            case NEW:
                return targetStatus == RUNNING || targetStatus == FAILED || 
                       targetStatus == KILLED || targetStatus == UNKNOWN;
            case RUNNING:
                return targetStatus == SUCCEEDED || targetStatus == FAILED || 
                       targetStatus == KILLED || targetStatus == UNKNOWN;
            default:
                return false;
        }
    }

    /**
     * 获取状态的显示颜色（用于UI展示）
     *
     * @return CSS颜色类名
     * <AUTHOR>
     */
    public String getDisplayColor() {
        switch (category) {
            case SUCCESS:
                return "text-success";
            case FAILED:
            case TERMINATED:
                return "text-danger";
            case ACTIVE:
                return "text-primary";
            case PENDING:
                return "text-warning";
            case UNKNOWN:
            default:
                return "text-muted";
        }
    }

    /**
     * 获取状态的详细描述
     *
     * @return 详细描述
     * <AUTHOR>
     */
    public String getDetailedDescription() {
        switch (this) {
            case NEW:
                return "任务已提交，等待开始执行";
            case RUNNING:
                return "任务正在执行中";
            case SUCCEEDED:
                return "任务执行成功完成";
            case FAILED:
                return "任务执行失败";
            case KILLED:
                return "任务被用户或系统终止";
            case UNKNOWN:
                return "任务状态未知，可能需要检查";
            default:
                return description;
        }
    }

    /**
     * 获取与BatchTaskStatus的映射关系
     *
     * @return 对应的BatchTaskStatus，如果无法映射返回null
     * <AUTHOR>
     */
    public com.trs.moye.batch.engine.enums.BatchTaskStatus toBatchTaskStatus() {
        switch (this) {
            case NEW:
                return com.trs.moye.batch.engine.enums.BatchTaskStatus.WAITING;
            case RUNNING:
                return com.trs.moye.batch.engine.enums.BatchTaskStatus.RUNNING;
            case SUCCEEDED:
                return com.trs.moye.batch.engine.enums.BatchTaskStatus.SUCCESS;
            case FAILED:
                return com.trs.moye.batch.engine.enums.BatchTaskStatus.FAILED;
            case KILLED:
                return com.trs.moye.batch.engine.enums.BatchTaskStatus.KILLED;
            case UNKNOWN:
            default:
                return null;
        }
    }

    /**
     * 获取状态的完整信息
     *
     * @return 状态信息字符串
     * <AUTHOR>
     */
    @Override
    public String toString() {
        return String.format("SparkAppStatus{code='%s', description='%s', category=%s, severityScore=%d}", 
                code, description, category, getSeverityScore());
    }
}