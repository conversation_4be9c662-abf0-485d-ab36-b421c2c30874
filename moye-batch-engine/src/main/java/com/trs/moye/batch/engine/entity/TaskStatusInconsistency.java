package com.trs.moye.batch.engine.entity;

import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.enums.SparkAppStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * 任务状态不一致记录实体
 * 增强版本，支持详细的不一致分析和修复跟踪
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskStatusInconsistency {

    /**
     * 执行ID
     */
    private String executeId;

    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据库中的状态
     */
    private BatchTaskStatus databaseStatus;

    /**
     * Spark中的实际状态
     */
    private SparkAppStatus sparkStatus;

    /**
     * 不一致发现时间
     */
    private LocalDateTime discoveredTime;

    /**
     * 修复状态
     */
    private FixStatus fixStatus;

    /**
     * 修复时间
     */
    private LocalDateTime fixTime;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 不一致类型
     */
    @Builder.Default
    private InconsistencyType inconsistencyType = InconsistencyType.STATUS_MISMATCH;

    /**
     * 修复尝试次数
     */
    @Builder.Default
    private int attemptCount = 0;

    /**
     * 最后修复尝试时间
     */
    private LocalDateTime lastAttemptTime;

    /**
     * 扩展信息
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 严重程度
     */
    @Builder.Default
    private Severity severity = Severity.MEDIUM;

    /**
     * 修复状态枚举
     */
    public enum FixStatus {
        /**
         * 待修复
         */
        PENDING,
        /**
         * 修复中
         */
        FIXING,
        /**
         * 修复成功
         */
        FIXED,
        /**
         * 修复失败
         */
        FAILED,
        /**
         * 跳过修复
         */
        SKIPPED
    }

    /**
     * 不一致类型枚举
     */
    public enum InconsistencyType {
        /**
         * 状态不匹配
         */
        STATUS_MISMATCH,
        /**
         * 任务丢失（数据库有记录但Spark无应用）
         */
        TASK_LOST,
        /**
         * 应用孤儿（Spark有应用但数据库无记录）
         */
        ORPHANED_APPLICATION,
        /**
         * 时间不一致
         */
        TIME_INCONSISTENCY,
        /**
         * 其他类型
         */
        OTHER
    }

    /**
     * 严重程度枚举
     */
    public enum Severity {
        /**
         * 低 - 轻微不一致，不影响业务
         */
        LOW,
        /**
         * 中 - 一般不一致，可能影响监控
         */
        MEDIUM,
        /**
         * 高 - 严重不一致，影响业务逻辑
         */
        HIGH,
        /**
         * 关键 - 关键不一致，必须立即处理
         */
        CRITICAL
    }

    /**
     * 创建新的状态不一致记录
     *
     * @param executeId      执行ID
     * @param applicationId  应用ID
     * @param taskName       任务名称
     * @param databaseStatus 数据库状态
     * @param sparkStatus    Spark状态
     * @return TaskStatusInconsistency
     */
    public static TaskStatusInconsistency create(String executeId, String applicationId, String taskName,
                                                 BatchTaskStatus databaseStatus, SparkAppStatus sparkStatus) {
        TaskStatusInconsistency inconsistency = TaskStatusInconsistency.builder()
                .executeId(executeId)
                .applicationId(applicationId)
                .taskName(taskName)
                .databaseStatus(databaseStatus)
                .sparkStatus(sparkStatus)
                .discoveredTime(LocalDateTime.now())
                .fixStatus(FixStatus.PENDING)
                .inconsistencyType(determineInconsistencyType(databaseStatus, sparkStatus))
                .severity(determineSeverity(databaseStatus, sparkStatus))
                .build();
        
        // 添加基本元数据
        inconsistency.addMetadata("createdBy", "SparkStatusChecker");
        inconsistency.addMetadata("detectionMethod", "automated");
        
        return inconsistency;
    }

    /**
     * 创建带类型的不一致记录
     *
     * @param executeId      执行ID
     * @param applicationId  应用ID
     * @param taskName       任务名称
     * @param databaseStatus 数据库状态
     * @param sparkStatus    Spark状态
     * @param type          不一致类型
     * @param severity      严重程度
     * @return TaskStatusInconsistency
     * <AUTHOR>
     */
    public static TaskStatusInconsistency create(String executeId, String applicationId, String taskName,
                                                BatchTaskStatus databaseStatus, SparkAppStatus sparkStatus,
                                                InconsistencyType type, Severity severity) {
        return TaskStatusInconsistency.builder()
                .executeId(executeId)
                .applicationId(applicationId)
                .taskName(taskName)
                .databaseStatus(databaseStatus)
                .sparkStatus(sparkStatus)
                .discoveredTime(LocalDateTime.now())
                .fixStatus(FixStatus.PENDING)
                .inconsistencyType(type)
                .severity(severity)
                .build();
    }

    /**
     * 标记为修复中
     */
    public void markAsFixing() {
        this.fixStatus = FixStatus.FIXING;
        this.attemptCount++;
        this.lastAttemptTime = LocalDateTime.now();
        addMetadata("lastAttemptBy", "TaskStatusRecoveryService");
    }

    /**
     * 标记为修复成功
     */
    public void markAsFixed() {
        this.fixStatus = FixStatus.FIXED;
        this.fixTime = LocalDateTime.now();
        addMetadata("fixedBy", "TaskStatusRecoveryService");
        addMetadata("totalAttempts", attemptCount);
    }

    /**
     * 标记为修复失败
     *
     * @param reason 失败原因
     */
    public void markAsFailed(String reason) {
        this.fixStatus = FixStatus.FAILED;
        this.failureReason = reason;
        this.fixTime = LocalDateTime.now();
        addMetadata("failureReason", reason);
        addMetadata("totalAttempts", attemptCount);
    }

    /**
     * 标记为跳过修复
     *
     * @param reason 跳过原因
     * <AUTHOR>
     */
    public void markAsSkipped(String reason) {
        this.fixStatus = FixStatus.SKIPPED;
        this.failureReason = reason;
        this.fixTime = LocalDateTime.now();
        addMetadata("skipReason", reason);
        addMetadata("totalAttempts", attemptCount);
    }

    /**
     * 判断是否需要修复
     *
     * @return 是否需要修复
     */
    public boolean needsFix() {
        return fixStatus == FixStatus.PENDING;
    }

    /**
     * 判断是否正在修复
     *
     * @return 是否正在修复
     * <AUTHOR>
     */
    public boolean isFixing() {
        return fixStatus == FixStatus.FIXING;
    }

    /**
     * 判断是否已修复
     *
     * @return 是否已修复
     * <AUTHOR>
     */
    public boolean isFixed() {
        return fixStatus == FixStatus.FIXED;
    }

    /**
     * 判断修复是否失败
     *
     * @return 修复是否失败
     * <AUTHOR>
     */
    public boolean isFixFailed() {
        return fixStatus == FixStatus.FAILED;
    }

    /**
     * 判断是否为高优先级（需要立即处理）
     *
     * @return 是否为高优先级
     * <AUTHOR>
     */
    public boolean isHighPriority() {
        return severity == Severity.HIGH || severity == Severity.CRITICAL;
    }

    /**
     * 添加元数据
     *
     * @param key 键
     * @param value 值
     * @return 当前实例
     * <AUTHOR>
     */
    public TaskStatusInconsistency addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
        return this;
    }

    /**
     * 获取元数据
     *
     * @param key 键
     * @return 值
     * <AUTHOR>
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }

    /**
     * 获取元数据，带默认值
     *
     * @param key 键
     * @param defaultValue 默认值
     * @param <T> 类型参数
     * @return 值或默认值
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public <T> T getMetadata(String key, T defaultValue) {
        if (metadata == null) {
            return defaultValue;
        }
        Object value = metadata.get(key);
        return value != null ? (T) value : defaultValue;
    }

    /**
     * 计算不一致持续时间
     *
     * @return 持续时间（分钟）
     * <AUTHOR>
     */
    public long getInconsistencyDurationMinutes() {
        if (discoveredTime == null) {
            return 0;
        }
        
        LocalDateTime endTime = fixTime != null ? fixTime : LocalDateTime.now();
        return java.time.Duration.between(discoveredTime, endTime).toMinutes();
    }

    /**
     * 获取不一致详细描述
     *
     * @return 详细描述
     * <AUTHOR>
     */
    public String getInconsistencyDetails() {
        return String.format("Inconsistency{executeId='%s', type=%s, severity=%s, database=%s, spark=%s, " +
                "fixStatus=%s, attempts=%d, duration=%dmin}", 
                executeId, inconsistencyType, severity, databaseStatus, sparkStatus, 
                fixStatus, attemptCount, getInconsistencyDurationMinutes());
    }

    /**
     * 确定不一致类型
     *
     * @param databaseStatus 数据库状态
     * @param sparkStatus Spark状态
     * @return 不一致类型
     * <AUTHOR>
     */
    private static InconsistencyType determineInconsistencyType(BatchTaskStatus databaseStatus, SparkAppStatus sparkStatus) {
        if (databaseStatus == null && sparkStatus != null) {
            return InconsistencyType.ORPHANED_APPLICATION;
        } else if (databaseStatus != null && sparkStatus == null) {
            return InconsistencyType.TASK_LOST;
        } else if (databaseStatus != null && sparkStatus != null) {
            return InconsistencyType.STATUS_MISMATCH;
        } else {
            return InconsistencyType.OTHER;
        }
    }

    /**
     * 确定严重程度
     *
     * @param databaseStatus 数据库状态
     * @param sparkStatus Spark状态
     * @return 严重程度
     * <AUTHOR>
     */
    private static Severity determineSeverity(BatchTaskStatus databaseStatus, SparkAppStatus sparkStatus) {
        // 任务丢失或孤儿应用为高严重程度
        if ((databaseStatus == null && sparkStatus != null) || 
            (databaseStatus != null && sparkStatus == null)) {
            return Severity.HIGH;
        }
        
        // 运行中任务状态不一致为中等严重程度
        if (databaseStatus == BatchTaskStatus.RUNNING && 
            (sparkStatus == SparkAppStatus.FAILED || sparkStatus == SparkAppStatus.KILLED)) {
            return Severity.MEDIUM;
        }
        
        // 已完成任务状态不一致为低严重程度
        if (databaseStatus != null && databaseStatus.isFinished() && 
            sparkStatus != null && sparkStatus.isFinished()) {
            return Severity.LOW;
        }
        
        // 默认中等严重程度
        return Severity.MEDIUM;
    }
}