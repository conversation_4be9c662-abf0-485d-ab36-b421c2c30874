package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.config.HadoopProperties;
import com.trs.moye.batch.engine.constants.SparkConstants.TaskParameter;
import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.enums.SparkAppStatus;
import com.trs.moye.batch.engine.exception.MinioOperationException;
import com.trs.moye.batch.engine.spark.SparkApplication;
import com.trs.moye.batch.engine.spark.SparkApplicationContext;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.SparkLogUtil;
import com.trs.moye.batch.engine.utils.yarnlog.YarnLogUtil;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


/**
 * SparkLogService 实现类
 * 负责Spark日志管理和状态监控，整合了原SparkRestApiClient功能
 * 
 * 主要功能：
 * 1. Spark应用日志收集和存储（Standalone和Yarn模式）
 * 2. Spark应用状态监控和查询
 * 3. 与现有监控系统的集成
 * 4. 基于Web UI和History Server的状态检查
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Slf4j
@Service
public class SparkLogService {

    @Resource
    private HadoopProperties hadoopProperties;
    @Resource
    private SparkApplicationConfig sparkApplicationConfig;
    @Resource
    private MinioService minioService;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${moye.batch.spark.status-check.enabled:true}")
    private boolean statusCheckEnabled;
    
    @Value("${moye.batch.spark.status-check.timeout-seconds:30}")
    private long statusCheckTimeoutSeconds;
    
    @Value("${moye.batch.spark.history-server.url:}")
    private String historyServerUrl;


    /**
     * 存储日志 1. standalone 模式下 spark集群上executor的日志 3. yarn 模式下 集群上的日志
     *
     * @param sparkApplication 根据 spark 任务拉取日志
     */
    public void storageLogs(SparkApplication sparkApplication) {
        // 如果是standalone模式拉取web ui上的日志
        storageStandaloneLog(sparkApplication);
        // 如果是yarn模式的拉取yarn日志
        storageYarnLog(sparkApplication);
    }

    /**
     * 请求web ui，存储到minio
     *
     * @param sparkApplication spark application
     */
    public void storageStandaloneLog(SparkApplication sparkApplication) {
        if (!sparkApplication.isStandaloneMode()) {
            log.info("非 standalone 模式，不拉取 web ui 日志");
            return;
        }

        try {
            Map<String, String> executorLogLinks = SparkLogUtil.getExecutorLogLinks(
                sparkApplicationConfig.getWebUiUrl(),
                sparkApplication.getApplicationId());
            String taskName = sparkApplication.getContext().getTaskName();
            for (Map.Entry<String, String> entry : executorLogLinks.entrySet()) {
                String logContent = SparkLogUtil.analyseExecutorLog(entry.getValue());
                minioService.saveTextToFile(sparkApplication.getContext().getLogPath() + "/" + taskName + "_" + entry.getKey(),
                    logContent);
            }
        } catch (URISyntaxException e) {
            log.warn("standalone web ui 相关地址解析异常", e);
        }

    }

    /**
     * 存放到本地临时目录，再复制到minio，删除本地日志
     *
     * @param sparkApplication spark 应用
     */
    private void storageYarnLog(SparkApplication sparkApplication) {
        if (!sparkApplication.isYarnMode()) {
            log.info("非 yarn 模式，不拉取 yarn 日志");
            return;
        }
        String yarnLogDir = "/tmp/yarn-log";
        SparkApplicationContext context = sparkApplication.getContext();
        Map<String, String> sparkTaskParameters = context.getConfigParameters();

        // 日志拉到本地
        String outputDir = Paths.get(yarnLogDir, context.getLogPath()).toString();
        File yarnLogFile = fetchYarnLog(
            sparkApplication.getApplicationId(),
            sparkTaskParameters.getOrDefault(TaskParameter.KERBEROS_PRINCIPAL, null),
            sparkTaskParameters.getOrDefault(TaskParameter.KERBEROS_KEYTAB, null),
            outputDir
        );

        // 复制到minio
        if (yarnLogFile != null) {
            try {
                String taskName = context.getTaskName();
                minioService.copyLocalFileToMinio(yarnLogDir, yarnLogFile, taskName);
                FileUtils.deleteDirectory(yarnLogFile);
            } catch (MinioOperationException e) {
                log.warn("复制文件 {} 到 minio 失败", yarnLogFile, e);
            } catch (IOException e) {
                log.warn("删除文件 {} 异常", yarnLogFile, e);
            }
        }
    }

    /**
     * 根据 appId 拉取日志，通过principal和keytab进行kerberos认证
     *
     * @param appId     需要拉取的spark任务id
     * @param principal kerberos用户
     * @param keytab    kerberos凭据
     * @param dir       拉取到的日志存放的本地目录路径
     * @return 实际存放文件的目录路径，如果拉取失败则返回null
     */
    private File fetchYarnLog(String appId, String principal, String keytab, String dir) {
        if (appId == null) {
            return null;
        }

        Set<String> resources = new HashSet<>(3);
        resources.add(hadoopProperties.getCoreSiteXmlPath());
        resources.add(hadoopProperties.getHdfsSiteXmlPath());
        resources.add(hadoopProperties.getYarnSiteXmlPath());

        try {
            if (principal != null) {
                YarnLogUtil.fetchLogs(appId, principal, keytab, dir, resources);
            } else {
                YarnLogUtil.fetchLogs(appId, dir, resources);
            }
            return new File(dir);
        } catch (IOException e) {
            log.error("拉取yarn日志失败", e);
            return null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }
    
    /**
     * 查询Spark应用状态信息
     * 优先使用Web UI，失败时尝试History Server
     *
     * @param applicationId Spark应用ID
     * @return Spark应用信息，失败时返回null
     * <AUTHOR>
     */
    public SparkApplicationInfo getSparkApplicationInfo(String applicationId) {
        if (!statusCheckEnabled || applicationId == null || applicationId.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 首先尝试从Web UI获取
            SparkApplicationInfo info = getApplicationInfoFromWebUI(applicationId);
            if (info != null) {
                return info;
            }
            
            // Web UI失败时尝试History Server
            if (historyServerUrl != null && !historyServerUrl.trim().isEmpty()) {
                return getApplicationInfoFromHistoryServer(applicationId);
            }
            
            return null;
            
        } catch (Exception e) {
            log.debug("获取Spark应用状态信息失败，应用ID: {}, 错误: {}", applicationId, e.getMessage());
            return null;
        }
    }
    
    /**
     * 从Web UI获取应用状态信息
     *
     * @param applicationId Spark应用ID
     * @return Spark应用信息
     * <AUTHOR>
     */
    private SparkApplicationInfo getApplicationInfoFromWebUI(String applicationId) {
        try {
            String webUiUrl = sparkApplicationConfig.getWebUiUrl();
            if (webUiUrl == null || webUiUrl.trim().isEmpty()) {
                return null;
            }
            
            String statusUrl = String.format("%s/api/v1/applications/%s", webUiUrl, applicationId);
            
            restTemplate.getRestTemplate().setRequestFactory(createTimeoutRequestFactory());
            Map<String, Object> response = restTemplate.getForObject(statusUrl, Map.class);
            
            return parseApplicationInfoFromResponse(response, applicationId);
            
        } catch (Exception e) {
            log.debug("从Web UI获取应用状态信息失败，应用ID: {}, 错误: {}", applicationId, e.getMessage());
            return null;
        }
    }
    
    /**
     * 从History Server获取应用状态信息
     *
     * @param applicationId Spark应用ID
     * @return Spark应用信息
     * <AUTHOR>
     */
    private SparkApplicationInfo getApplicationInfoFromHistoryServer(String applicationId) {
        try {
            String statusUrl = String.format("%s/api/v1/applications/%s", historyServerUrl, applicationId);
            
            restTemplate.getRestTemplate().setRequestFactory(createTimeoutRequestFactory());
            Map<String, Object> response = restTemplate.getForObject(statusUrl, Map.class);
            
            return parseApplicationInfoFromResponse(response, applicationId);
            
        } catch (Exception e) {
            log.debug("从History Server获取应用状态信息失败，应用ID: {}, 错误: {}", applicationId, e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析REST API响应为SparkApplicationInfo
     *
     * @param response REST响应
     * @param applicationId 应用ID
     * @return 解析后的应用信息
     * <AUTHOR>
     */
    private SparkApplicationInfo parseApplicationInfoFromResponse(Map<String, Object> response, String applicationId) {
        if (response == null) {
            return null;
        }
        
        try {
            String id = (String) response.get("id");
            String name = (String) response.get("name");
            Object attempts = response.get("attempts");
            
            if (attempts instanceof List && !((List<?>) attempts).isEmpty()) {
                Map<String, Object> attempt = (Map<String, Object>) ((List<?>) attempts).get(0);
                
                String startTimeStr = (String) attempt.get("startTime");
                String endTimeStr = (String) attempt.get("endTime");
                Boolean completed = (Boolean) attempt.get("completed");
                
                SparkAppStatus status = parseSparkAppStatus(completed, endTimeStr);
                LocalDateTime startTime = parseDateTime(startTimeStr);
                LocalDateTime endTime = parseDateTime(endTimeStr);
                
                return new SparkApplicationInfo(id, name, status, startTime, endTime);
            }
            
            return new SparkApplicationInfo(applicationId, name, SparkAppStatus.UNKNOWN, null, null);
            
        } catch (Exception e) {
            log.debug("解析应用状态响应失败，应用ID: {}, 错误: {}", applicationId, e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析Spark应用状态
     *
     * @param completed 是否完成
     * @param endTimeStr 结束时间字符串
     * @return Spark应用状态
     * <AUTHOR>
     */
    private SparkAppStatus parseSparkAppStatus(Boolean completed, String endTimeStr) {
        if (completed != null && completed) {
            return SparkAppStatus.SUCCEEDED;
        } else if (endTimeStr != null && !endTimeStr.trim().isEmpty()) {
            return SparkAppStatus.FAILED;
        } else {
            return SparkAppStatus.RUNNING;
        }
    }
    
    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     * <AUTHOR>
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // Spark REST API返回的时间格式通常是ISO格式
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        } catch (Exception e) {
            try {
                // 尝试其他可能的格式
                return LocalDateTime.parse(dateTimeStr.replace("T", " ").replace("Z", ""));
            } catch (Exception ex) {
                log.debug("解析时间字符串失败: {}", dateTimeStr);
                return null;
            }
        }
    }
    
    /**
     * 创建带超时配置的请求工厂
     *
     * @return 配置了超时的请求工厂
     * <AUTHOR>
     */
    private org.springframework.http.client.ClientHttpRequestFactory createTimeoutRequestFactory() {
        org.springframework.http.client.SimpleClientHttpRequestFactory factory = 
                new org.springframework.http.client.SimpleClientHttpRequestFactory();
        
        int timeoutMs = (int) TimeUnit.SECONDS.toMillis(statusCheckTimeoutSeconds);
        factory.setConnectTimeout(timeoutMs);
        factory.setReadTimeout(timeoutMs);
        
        return factory;
    }
    
    /**
     * 批量查询多个Spark应用的状态信息
     *
     * @param applicationIds 应用ID列表
     * @return 应用ID到应用信息的映射
     * <AUTHOR>
     */
    public Map<String, SparkApplicationInfo> batchGetSparkApplicationInfo(List<String> applicationIds) {
        if (!statusCheckEnabled || applicationIds == null || applicationIds.isEmpty()) {
            return Collections.emptyMap();
        }
        
        Map<String, SparkApplicationInfo> results = new HashMap<>();
        
        for (String applicationId : applicationIds) {
            if (applicationId != null && !applicationId.trim().isEmpty()) {
                SparkApplicationInfo info = getSparkApplicationInfo(applicationId);
                if (info != null) {
                    results.put(applicationId, info);
                }
            }
        }
        
        return results;
    }
    
    /**
     * 检查Spark应用是否仍在运行
     *
     * @param applicationId Spark应用ID
     * @return 如果应用正在运行返回true，否则返回false
     * <AUTHOR>
     */
    public boolean isSparkApplicationRunning(String applicationId) {
        SparkApplicationInfo info = getSparkApplicationInfo(applicationId);
        return info != null && info.getStatus() == SparkAppStatus.RUNNING;
    }
    
    /**
     * 获取状态检查服务的配置信息
     *
     * @return 配置信息映射
     * <AUTHOR>
     */
    public Map<String, Object> getStatusCheckConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("statusCheckEnabled", statusCheckEnabled);
        config.put("statusCheckTimeoutSeconds", statusCheckTimeoutSeconds);
        config.put("webUiUrl", sparkApplicationConfig.getWebUiUrl());
        config.put("historyServerUrl", historyServerUrl);
        return config;
    }
}
