package com.trs.moye.batch.engine.config;

import com.trs.moye.batch.engine.service.EnhancedStartupRecovery;
import com.trs.moye.batch.engine.service.SparkRestApiClient;
import com.trs.moye.batch.engine.service.SparkStatusChecker;
import com.trs.moye.batch.engine.service.TaskLifecycleTracker;
import com.trs.moye.batch.engine.service.TaskStatusRecoveryService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * 批处理引擎状态管理自动配置类 提供任务状态一致性检查和恢复功能的最小化配置
 * <p>
 * 主要功能： 1. 自动配置所有状态管理组件 2. 提供默认的OkHttp客户端配置 3. 优雅的服务关闭处理 4. 可通过配置开关控制功能启用
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "moye.batch.status.management.enabled", havingValue = "true", matchIfMissing = true)
public class BatchEngineStatusAutoConfiguration {

    @Value("${moye.batch.okhttp.connect-timeout:10}")
    private int connectTimeoutSeconds;

    @Value("${moye.batch.okhttp.read-timeout:30}")
    private int readTimeoutSeconds;

    @Value("${moye.batch.okhttp.write-timeout:30}")
    private int writeTimeoutSeconds;

    @Value("${moye.batch.okhttp.max-idle-connections:5}")
    private int maxIdleConnections;

    @Value("${moye.batch.okhttp.keep-alive-duration:300}")
    private int keepAliveDurationSeconds;

    /**
     * 配置OkHttp客户端 用于Spark REST API调用
     */
    @Bean
    @ConditionalOnProperty(name = "moye.batch.okhttp.enabled", havingValue = "true", matchIfMissing = true)
    public OkHttpClient okHttpClient() {
        log.info("配置OkHttp客户端，连接超时: {}s, 读取超时: {}s", connectTimeoutSeconds, readTimeoutSeconds);

        return new OkHttpClient.Builder()
            .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
            .readTimeout(readTimeoutSeconds, TimeUnit.SECONDS)
            .writeTimeout(writeTimeoutSeconds, TimeUnit.SECONDS)
            .connectionPool(new okhttp3.ConnectionPool(maxIdleConnections, keepAliveDurationSeconds, TimeUnit.SECONDS))
            .retryOnConnectionFailure(true)
            .build();
    }

    /**
     * Spark REST API客户端
     */
    @Bean
    @ConditionalOnProperty(name = "moye.batch.spark.rest-api.enabled", havingValue = "true", matchIfMissing = true)
    public SparkRestApiClient sparkRestApiClient() {
        log.info("配置Spark REST API客户端");
        return new SparkRestApiClient();
    }

    /**
     * Spark状态检查器
     */
    @Bean
    @ConditionalOnProperty(name = "moye.batch.status.checker.enabled", havingValue = "true", matchIfMissing = true)
    public SparkStatusChecker sparkStatusChecker() {
        log.info("配置Spark状态检查器");
        return new SparkStatusChecker();
    }

    /**
     * 任务状态恢复服务
     */
    @Bean
    @ConditionalOnProperty(name = "moye.batch.status.recovery.enabled", havingValue = "true", matchIfMissing = true)
    public TaskStatusRecoveryService taskStatusRecoveryService() {
        log.info("配置任务状态恢复服务");
        return new TaskStatusRecoveryService();
    }

    /**
     * 增强的启动恢复服务
     */
    @Bean
    @ConditionalOnProperty(name = "moye.batch.startup.recovery.enabled", havingValue = "true", matchIfMissing = true)
    public EnhancedStartupRecovery enhancedStartupRecovery() {
        log.info("配置增强的启动恢复服务");
        return new EnhancedStartupRecovery();
    }

    /**
     * 任务生命周期跟踪器
     */
    @Bean
    @ConditionalOnProperty(name = "moye.batch.lifecycle.tracker.enabled", havingValue = "true", matchIfMissing = true)
    public TaskLifecycleTracker taskLifecycleTracker() {
        log.info("配置任务生命周期跟踪器");
        return new TaskLifecycleTracker();
    }


    /**
     * 应用启动完成后的初始化处理
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("批处理引擎状态管理组件初始化完成");

        // 这里可以添加一些启动后的初始化逻辑
        // 比如连接性测试、配置验证等
        validateConfiguration();
    }

    /**
     * 验证配置有效性
     */
    private void validateConfiguration() {
        try {
            log.debug("开始验证批处理引擎状态管理配置");

            // 验证超时配置
            if (connectTimeoutSeconds <= 0 || readTimeoutSeconds <= 0) {
                log.warn("OkHttp超时配置可能不合理: connect={}s, read={}s", connectTimeoutSeconds, readTimeoutSeconds);
            }

            // 验证连接池配置
            if (maxIdleConnections <= 0 || keepAliveDurationSeconds <= 0) {
                log.warn("OkHttp连接池配置可能不合理: maxIdle={}, keepAlive={}s", maxIdleConnections,
                    keepAliveDurationSeconds);
            }

            log.debug("批处理引擎状态管理配置验证完成");

        } catch (Exception e) {
            log.warn("配置验证时发生异常", e);
        }
    }

    /**
     * 应用关闭时的清理处理
     */
    @PreDestroy
    public void onDestroy() {
        log.info("批处理引擎状态管理组件开始关闭清理");

        // 这里可以添加一些关闭前的清理逻辑
        // 比如停止后台任务、释放资源等
        // 注意：Spring会自动调用各个Bean的销毁方法
    }

    /**
     * 配置信息输出Bean 用于启动时输出配置信息，方便调试
     */
    @Bean
    public ConfigurationInfo configurationInfo() {
        return new ConfigurationInfo();
    }

    /**
     * 配置信息类
     */
    public static class ConfigurationInfo {

        @EventListener(ApplicationReadyEvent.class)
        public void printConfiguration() {
            log.info("=== 批处理引擎状态管理配置信息 ===");
            log.info("功能状态:");
            log.info("  - 状态检查器: {}", getPropertyValue("moye.batch.status.checker.enabled", "true"));
            log.info("  - 状态恢复: {}", getPropertyValue("moye.batch.status.recovery.enabled", "true"));
            log.info("  - 启动恢复: {}", getPropertyValue("moye.batch.startup.recovery.enabled", "true"));
            log.info("  - 生命周期跟踪: {}", getPropertyValue("moye.batch.lifecycle.tracker.enabled", "true"));
            log.info("  - 状态缓存: {}", getPropertyValue("moye.batch.status.cache.enabled", "true"));

            log.info("Spark配置:");
            log.info("  - History Server: {}",
                getPropertyValue("moye.batch.spark.history-server.url", "http://localhost:18080"));
            log.info("  - API超时: {}", getPropertyValue("moye.batch.spark.rest-api.timeout", "30000"));
            log.info("  - 重试次数: {}", getPropertyValue("moye.batch.spark.rest-api.retry-count", "3"));

            log.info("缓存配置:");
            log.info("  - 缓存过期: {}分钟", getPropertyValue("moye.batch.status.cache.expire-minutes", "30"));
            log.info("  - 心跳过期: {}分钟",
                getPropertyValue("moye.batch.status.cache.heartbeat-expire-minutes", "10"));

            log.info("跟踪配置:");
            log.info("  - 初始延迟: {}秒", getPropertyValue("moye.batch.lifecycle.tracker.initial-delay", "30"));
            log.info("  - 最大跟踪: {}小时",
                getPropertyValue("moye.batch.lifecycle.tracker.max-tracking-duration", "24"));

            log.info("恢复配置:");
            log.info("  - 任务最大年龄: {}小时", getPropertyValue("moye.batch.startup.recovery.max-age-hours", "72"));
            log.info("  - 单节点同步: 启用AtomicBoolean + synchronized机制");

            log.info("======================================");
        }

        private String getPropertyValue(String key, String defaultValue) {
            return System.getProperty(key, defaultValue);
        }
    }
}