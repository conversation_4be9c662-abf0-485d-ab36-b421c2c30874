package com.trs.moye.batch.engine.xxljob;

import com.trs.ai.moye.schedule.starter.xxljob.XXLJobManager;
import com.trs.ai.moye.schedule.starter.xxljob.entity.MoyeXxlJob;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.CodeParameterItem;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.SparkConfigItem;
import com.trs.moye.base.data.schedule.ScheduleTypeEnum;
import com.trs.moye.batch.engine.config.ExecutorConfig;
import com.trs.moye.batch.engine.dao.BatchArrangementMapper;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchArrangement;
import com.trs.moye.batch.engine.entity.BatchCodeSubtask;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.BatchTaskRecord.TaskWriteCount;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import com.trs.moye.batch.engine.entity.vo.BatchTaskVO;
import com.trs.moye.batch.engine.enums.ArrangeDisplayType;
import com.trs.moye.batch.engine.enums.TriggerModeEnum;
import com.trs.moye.batch.engine.exception.XxlJobException;
import com.trs.moye.batch.engine.properties.RetryJobProperties;
import com.trs.moye.batch.engine.service.BatchTaskMonitor;
import com.trs.moye.batch.engine.service.BatchTaskService;
import com.trs.moye.batch.engine.utils.BatchIdUtil;
import com.trs.moye.batch.engine.xxljob.enums.AppJobHandler;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * xxl 任务列表
 *
 * <AUTHOR>
 * @since 2024/06/21
 */
@Slf4j
@Component
public class XxlJobs {

    @Resource(name = ExecutorConfig.SCHEDULED_TASK_EXECUTOR)
    private ExecutorService scheduledTaskExecutor;
    @Resource
    private BatchTaskService batchTaskService;
    @Resource
    private BatchArrangementMapper batchArrangementMapper;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;
    @Resource
    private AuthCertificateMapper authCertificateMapper;
    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;
    @Resource
    private XXLJobManager xxlJobManager;
    @Resource
    private RetryJobProperties retryJobProperties;


    /**
     * 批处理任务执行器
     */
    @XxlJob("moye-batch-engine-job")
    public void execute() {
        try {
            XxlTaskParam taskParam = JsonUtils.parseObject(XxlJobHelper.getJobParam(), XxlTaskParam.class);
            if (taskParam == null || taskParam.getTaskId() == null) {
                throw new BizException("xxl-job 任务参数为空");
            }
            // 执行任务
            executeXxlTask(taskParam.getTaskId(), Math.toIntExact(XxlJobHelper.getJobId()));
        } catch (Exception e) {
            String errorMsg = String.format("【%s】任务执行异常", XxlJobHelper.getJobParam());
            throw new XxlJobException(e, errorMsg);
        }
    }

    private void executeXxlTask(Integer dataModelId, Integer xxlJobId) {

        // 查询数据建模
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        AssertUtils.notEmpty(dataModel, "主键为【%s】的数据建模不存在", dataModelId);

        // task
        BatchTaskVO taskVO = new BatchTaskVO();
        taskVO.setTaskId(dataModelId.toString());
        taskVO.setTaskName(dataModel.getZhName());
        taskVO.setLayer(dataModel.getLayer());
        taskVO.setExecuteId(BatchIdUtil.buildExecuteId());

        // 查询上次调度
        BatchTaskRecord lastBatchTaskRecord = batchTaskRecordMapper.getPreviousBatchTaskRecordByTriggerMode(
            dataModelId, TriggerModeEnum.FIXED_TIME, null);
        if (lastBatchTaskRecord != null && lastBatchTaskRecord.getEndTime() == null) {
            // 创建 xxl-job 重试任务
            String retryXxlJobName = buildRetryXxlJobName(dataModel.getLayer(), dataModel.getZhName(),
                dataModel.getId(), taskVO.getExecuteId());
            LocalDateTime nextExecuteTime = xxlJobManager.getNextOneExecuteTime(xxlJobId);
            String fixRate = calculateFixRate(nextExecuteTime);
            MoyeXxlJob retryMoyeXxlJob = buildRetryXxlJob(retryXxlJobName, fixRate,
                new RetryXxlTaskParam(dataModelId, taskVO.getExecuteId(), 0, retryJobProperties.getMaxRetryCount()));
            int retryXxlJobId = xxlJobManager.createJob(retryMoyeXxlJob).getId();

            // record 入库
            BatchTaskMonitor.insertOrUpdateRecord(
                BatchTaskRecord.waitTask(taskVO, TriggerModeEnum.FIXED_TIME, LocalDateTime.now(), retryXxlJobId));

            // tracer 入库
            BatchTaskMonitor.insertTracer(BatchTaskTracer.waitTask(taskVO.getExecuteId(),
                lastBatchTaskRecord.getExecuteId(), 0, retryJobProperties.getMaxRetryCount()));

            // 启动任务
            xxlJobManager.startJob(retryXxlJobId);
            return;
        }

        asyncExecuteTask(dataModel, taskVO);
    }

    private String buildRetryXxlJobName(ModelLayer layer, String zhName, Integer id, String executeId) {
        return String.format("等待重试任务-%s-%s-%s-%s", layer.getLabel(), zhName, id, executeId);
    }

    /**
     * 距离下一次执行的时长的一定比例(可配置,默认0.6), 除以重试次数（可配置,默认3）, 与 1 小时取最小值
     *
     * @param nextExecuteTime 下次执行时间
     * @return 重试间隔(秒)
     */
    private String calculateFixRate(LocalDateTime nextExecuteTime) {
        // 计算当前时间到下次执行时间的秒数差
        long seconds = Duration.between(LocalDateTime.now(), nextExecuteTime).getSeconds();

        // 应用配置的比例和重试次数计算间隔
        long interval = (long) (seconds * retryJobProperties.getTimeRatio() / retryJobProperties.getMaxRetryCount());

        // 确保间隔在最小和最大限制之间
        interval = Math.max(interval, retryJobProperties.getMinInterval());
        interval = Math.min(interval, retryJobProperties.getMaxInterval());

        // 转换为XXL-JOB的FIX_RATE格式 (单位：秒)
        return String.valueOf(interval);
    }

    private MoyeXxlJob buildRetryXxlJob(String xxlJobName, String fixRate, RetryXxlTaskParam retryXxlTaskParam) {
        AppJobHandler jobHandler = AppJobHandler.BATCH_ENGINE_MODEL_RETRY_TASK;
        return MoyeXxlJob.builder()
            .jobName(xxlJobName)
            .jobParam(JsonUtils.toJsonString(retryXxlTaskParam))
            .app(jobHandler.getApp())
            .jobHandler(jobHandler.getJobHandler())
            .scheduleType(ScheduleTypeEnum.FIX_RATE)
            .scheduleConf(fixRate)
            .build();
    }


    /**
     * 批处理重试任务执行器
     */
    @XxlJob("moye-batch-engine-job-retry")
    public void executeRetry() {
        try {
            RetryXxlTaskParam taskParam = JsonUtils.parseObject(XxlJobHelper.getJobParam(), RetryXxlTaskParam.class);
            if (taskParam == null || taskParam.getTaskId() == null) {
                throw new BizException("xxl-job 任务参数为空");
            }
            executeRetryXxlTask(taskParam, Math.toIntExact(XxlJobHelper.getJobId()));
        } catch (Exception e) {
            String errorMsg = String.format("【%s】任务执行异常", XxlJobHelper.getJobParam());
            throw new XxlJobException(e, errorMsg);
        }
    }

    private void executeRetryXxlTask(RetryXxlTaskParam taskParam, Integer xxlJobId) {

        // 前序任务还未结束, 继续等待
        BatchTaskRecord lastBatchTaskRecord = batchTaskRecordMapper.getPreviousBatchTaskRecordByTriggerMode(
            taskParam.getTaskId(), TriggerModeEnum.FIXED_TIME, taskParam.getExecuteId());
        if (lastBatchTaskRecord != null && lastBatchTaskRecord.getEndTime() == null) {
            // 更新重试次数
            taskParam.setRetryCount(taskParam.getRetryCount() + 1);

            if (taskParam.getRetryCount() >= taskParam.getMaxRetryCount()) {
                // 删除xxl-job重试任务
                log.info("batch task record [executeId:{}] 重试次数超过最大重试次数 [maxRetryCount:{}]，删除xxl-job重试任务 [xxlJobId:{}]",
                    taskParam.getExecuteId(), taskParam.getMaxRetryCount(), xxlJobId);
                xxlJobManager.deleteJob(xxlJobId);
                // 记录丢弃任务的日志
                BatchTaskMonitor.insertTracer(
                    BatchTaskTracer.discardTask(taskParam.getExecuteId(), lastBatchTaskRecord.getExecuteId(),
                        taskParam.getRetryCount(), taskParam.getMaxRetryCount()));
                // 标记任务结束
                BatchTaskMonitor.insertOrUpdateRecord(BatchTaskRecord.discardTask(taskParam.getExecuteId()));
            } else {
                // 更新xxl-job任务, 更新重试次数
                xxlJobManager.updateJob(xxlJobId, JsonUtils.toJsonString(taskParam));
                // 记录等待任务的日志
                BatchTaskMonitor.insertTracer(
                    BatchTaskTracer.waitTask(taskParam.getExecuteId(), lastBatchTaskRecord.getExecuteId(),
                        taskParam.getRetryCount(), taskParam.getMaxRetryCount()));
            }
            return;
        }

        // 删除重试任务
        log.info("batch task record [executeId:{}] 重试任务开始执行，删除xxl-job重试任务 [xxlJobId:{}]", taskParam.getExecuteId(),
            xxlJobId);
        xxlJobManager.deleteJob(xxlJobId);

        // 查询数据建模
        DataModel dataModel = dataModelMapper.selectById(taskParam.getTaskId());
        AssertUtils.notEmpty(dataModel, "主键为【%s】的数据建模不存在", taskParam.getTaskId());

        // task
        BatchTaskVO taskVO = new BatchTaskVO();
        taskVO.setTaskId(dataModel.getId().toString());
        taskVO.setTaskName(dataModel.getZhName());
        taskVO.setLayer(dataModel.getLayer());
        taskVO.setExecuteId(taskParam.getExecuteId());

        asyncExecuteTask(dataModel, taskVO);
    }

    private void asyncExecuteTask(DataModel dataModel, BatchTaskVO taskVO) {
        // record 入库
        BatchTaskMonitor.insertOrUpdateRecord(
            BatchTaskRecord.beginTask(taskVO, TriggerModeEnum.FIXED_TIME, LocalDateTime.now()));
        scheduledTaskExecutor.submit(() -> executeTask(dataModel, taskVO));
        log.info("batch task [executeId:{}] 定时任务提到到线程池", taskVO.getExecuteId());
    }

    private void executeTask(DataModel dataModel, BatchTaskVO taskVO) {
        log.info("batch task [executeId:{}] 定时任务获取到线程开始执行", taskVO.getExecuteId());

        LocalDateTime beginTime = LocalDateTime.now();
        try {
            // 查询执行配置: 任务参数以及kerberos信息
            DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(dataModel.getId());
            AssertUtils.notEmpty(executeConfig, "数据建模【%s】未配置执行配置信息", dataModel.getZhName());
            BatchProcessSparkConfig sparkConfig = executeConfig.getSparkConfig();
            KerberosCertificate kerberosCertificate = null;
            Map<String, String> sparkConfigParameters = new HashMap<>();
            Map<String, String> customCodeParameters = new HashMap<>();
            if (ObjectUtils.isNotEmpty(sparkConfig)) {
                // kerberos
                kerberosCertificate = ObjectUtils.isEmpty(sparkConfig.getCertificateId()) ? null
                    : authCertificateMapper.selectById(sparkConfig.getCertificateId());
                // 任务参数
                sparkConfigParameters = getSparkConfigParameters(sparkConfig.getSparkConfigItemList());
                customCodeParameters = CodeParameterItem.convertToMap(sparkConfig.getCustomCodeParameters());
            }

            // 查询编排
            ArrangeDisplayType displayType;
            BatchArrangement arrangement = null;
            //指标库默认dag模式；无需查询编排
            if (dataModel.getLayer().equals(ModelLayer.INDICATOR)) {
                displayType = ArrangeDisplayType.CANVAS;
            } else {
                arrangement = batchArrangementMapper.selectByDataModelId(dataModel.getId());
                AssertUtils.notEmpty(arrangement, "数据建模【%s】未配置批处理编排信息", dataModel.getZhName());
                displayType = arrangement.getDisplayType();
            }

            //记录触发日志
            BatchTaskMonitor.insertTracer(BatchTaskTracer.beginTask(taskVO, beginTime, LocalDateTime.now(), null));
            // 代码模式和DAG模式分流
            if (displayType == ArrangeDisplayType.CODE) {
                List<BatchCodeSubtask> subTasks = arrangement.getCodeSubTasks();
                AssertUtils.notEmpty(subTasks, "数据建模【%s】未配置子任务信息", dataModel.getZhName());
                executeCodeModeTask(taskVO, subTasks, sparkConfigParameters, kerberosCertificate, customCodeParameters,
                    sparkConfig.getContinueOnException());
            } else {
                executeDagModeTask(taskVO, sparkConfigParameters, kerberosCertificate, customCodeParameters);
            }
        } catch (Exception e) {
            //记录触发日志
            BatchTaskMonitor.insertTracer(BatchTaskTracer.beginTask(taskVO, beginTime, LocalDateTime.now(), e));
            BatchTaskMonitor.insertOrUpdateRecord(
                BatchTaskRecord.endTask(taskVO.getExecuteId(), e, LocalDateTime.now(), 0L, new TaskWriteCount[0]));
            log.error("任务触发失败！任务id：{}", dataModel.getId(), e);
        }
    }

    private Map<String, String> getSparkConfigParameters(List<SparkConfigItem> sparkConfigItemList) {
        Map<String, String> configParameters = new HashMap<>();
        // 设置spark配置信息
        if (ObjectUtils.isEmpty(sparkConfigItemList)) {
            return configParameters;
        }
        for (SparkConfigItem configItem : sparkConfigItemList) {
            if (ObjectUtils.isNotEmpty(configItem.getKey()) && ObjectUtils.isNotEmpty(configItem.getValue())) {
                configParameters.put(configItem.getKey(), configItem.getValue());
            }
        }
        return configParameters;
    }

    private void executeDagModeTask(BatchTaskVO taskVO, Map<String, String> sparkConfigParameters,
        KerberosCertificate kerberosCertificate, Map<String, String> customCodeParameters) {
        taskVO.setConfigParameters(sparkConfigParameters);
        if (Objects.nonNull(kerberosCertificate)) {
            taskVO.setPrincipal(kerberosCertificate.getPrincipal());
            taskVO.setKeytabPath(kerberosCertificate.getKeytabPath());
        }
        taskVO.setCustomCodeParameters(customCodeParameters);
        batchTaskService.executeDagTask(taskVO, false);
    }

    private void executeCodeModeTask(BatchTaskVO taskVO, List<BatchCodeSubtask> subTasks,
        Map<String, String> configParameters, KerberosCertificate kerberosCertificate,
        Map<String, String> customCodeParameters, Boolean continueOnException) {
        List<BatchTaskVO> taskVOList = new ArrayList<>();
        for (BatchCodeSubtask subTask : subTasks) {
            BatchTaskVO vo = new BatchTaskVO();
            // 设置建模信息
            vo.setTaskId(taskVO.getTaskId());
            vo.setTaskName(taskVO.getTaskName());
            vo.setLayer(taskVO.getLayer());
            vo.setExecuteId(taskVO.getExecuteId());
            // 设置kerberos信息
            if (Objects.nonNull(kerberosCertificate)) {
                vo.setPrincipal(kerberosCertificate.getPrincipal());
                vo.setKeytabPath(kerberosCertificate.getKeytabPath());
            }
            // 设置spark配置参数
            vo.setConfigParameters(configParameters);
            vo.setCustomCodeParameters(customCodeParameters);
            // 设置子任务信息
            vo.setSubTaskName(subTask.getName());
            vo.setCodeType(subTask.getCodeType());
            vo.setCode(subTask.getCode());
            vo.setContinueOnException(continueOnException);
            taskVOList.add(vo);
        }
        batchTaskService.executeCodeTasks(taskVOList, false);
    }

}
