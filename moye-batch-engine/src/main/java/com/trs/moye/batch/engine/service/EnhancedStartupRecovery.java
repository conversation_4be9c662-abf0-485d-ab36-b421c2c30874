package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.TaskStatusInconsistency;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 增强的启动时状态恢复服务 负责服务启动时智能检测和修复任务状态不一致问题
 * <p>
 * 主要特性： 1. 智能筛选需要检查的任务 2. 批量并行处理提高效率 3. 优先级排序(最近的任务优先) 4. 详细的恢复统计和报告
 *
 * <AUTHOR>
 * @since 2025/08/28
 */
@Slf4j
@Service
public class EnhancedStartupRecovery {

    @Autowired
    private SparkStatusChecker sparkStatusChecker;

    @Autowired
    private TaskStatusRecoveryService taskStatusRecoveryService;

    @Autowired
    private BatchTaskRecordMapper batchTaskRecordMapper;

    @Value("${moye.batch.startup.recovery.enabled:true}")
    private boolean recoveryEnabled;

    @Value("${moye.batch.startup.recovery.max-age-hours:72}")
    private long maxAgeHours;

    @Value("${moye.batch.startup.recovery.batch-size:20}")
    private int batchSize;

    @Value("${moye.batch.startup.recovery.max-concurrent:5}")
    private int maxConcurrent;

    /**
     * 执行启动时的完整状态恢复
     *
     * @return 恢复统计结果
     */
    public RecoveryStatistics performStartupRecovery() {
        if (!recoveryEnabled) {
            log.info("启动恢复功能已禁用，跳过恢复");
            return RecoveryStatistics.disabled();
        }

        log.info("开始执行启动时的智能状态恢复");
        RecoveryStatistics statistics = new RecoveryStatistics();

        try {
            // 第一步：获取候选任务列表
            List<BatchTaskRecord> candidateTasks = getCandidateTasksForRecovery();
            statistics.setCandidateCount(candidateTasks.size());

            if (candidateTasks.isEmpty()) {
                log.info("未发现需要检查状态的候选任务");
                return statistics;
            }

            log.info("发现{}个候选任务需要状态检查", candidateTasks.size());

            // 第二步：批量检查状态不一致
            List<TaskStatusInconsistency> inconsistencies = batchCheckTaskStatus(candidateTasks, statistics);

            if (inconsistencies.isEmpty()) {
                log.info("所有候选任务状态正常，无需修复");
                return statistics;
            }

            // 第三步：按优先级排序并批量修复
            inconsistencies = prioritizeInconsistencies(inconsistencies);
            batchFixInconsistencies(inconsistencies, statistics);

            log.info("启动恢复完成: {}", statistics.getSummary());
            return statistics;

        } catch (Exception e) {
            log.error("启动时状态恢复发生异常", e);
            statistics.addError("启动恢复异常: " + e.getMessage());
            return statistics;
        }
    }

    /**
     * 获取需要恢复检查的候选任务 智能筛选策略： 1. 状态为RUNNING的任务 2. 最近N小时内的任务 3. 有applicationId的任务
     *
     * @return 候选任务列表
     */
    private List<BatchTaskRecord> getCandidateTasksForRecovery() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(maxAgeHours);

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("status", BatchTaskStatus.RUNNING);

        List<BatchTaskRecord> runningTasks = batchTaskRecordMapper.selectByMap(queryParams);

        return runningTasks.stream()
            .filter(task -> task.getStartTime().isAfter(cutoffTime))
            .filter(task -> task.getApplicationId() != null && !task.getApplicationId().trim().isEmpty())
            .sorted((t1, t2) -> t2.getStartTime().compareTo(t1.getStartTime())) // 最新的任务优先
            .collect(Collectors.toList());
    }

    /**
     * 批量检查任务状态
     *
     * @param candidateTasks 候选任务
     * @param statistics     统计信息
     * @return 不一致的任务列表
     */
    private List<TaskStatusInconsistency> batchCheckTaskStatus(List<BatchTaskRecord> candidateTasks,
        RecoveryStatistics statistics) {
        List<TaskStatusInconsistency> inconsistencies = new ArrayList<>();
        int checkedCount = 0;

        // 分批处理，避免一次性检查太多任务
        for (int i = 0; i < candidateTasks.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, candidateTasks.size());
            List<BatchTaskRecord> batch = candidateTasks.subList(i, endIndex);

            log.debug("检查第{}批任务，任务数量: {}", (i / batchSize) + 1, batch.size());

            for (BatchTaskRecord task : batch) {
                try {
                    TaskStatusInconsistency inconsistency = sparkStatusChecker.checkSingleTaskStatus(
                        task.getExecuteId());
                    checkedCount++;

                    if (inconsistency != null) {
                        inconsistencies.add(inconsistency);
                        log.info("发现状态不一致任务: executeId={}, 数据库状态={}, Spark状态={}",
                            inconsistency.getExecuteId(),
                            inconsistency.getDatabaseStatus(),
                            inconsistency.getSparkStatus());
                    }
                } catch (Exception e) {
                    log.warn("检查任务状态时发生异常，执行ID: " + task.getExecuteId(), e);
                    statistics.addError("检查任务异常: " + task.getExecuteId() + " - " + e.getMessage());
                }
            }

            // 添加批次间的短暂延迟，避免对系统造成过大压力
            if (i + batchSize < candidateTasks.size()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        statistics.setCheckedCount(checkedCount);
        statistics.setInconsistencyCount(inconsistencies.size());
        return inconsistencies;
    }

    /**
     * 对状态不一致的任务进行优先级排序
     *
     * @param inconsistencies 不一致列表
     * @return 排序后的列表
     */
    private List<TaskStatusInconsistency> prioritizeInconsistencies(List<TaskStatusInconsistency> inconsistencies) {
        return inconsistencies.stream()
            .sorted((i1, i2) -> {
                // 优先级1: FAILED和KILLED状态优先修复
                int priority1 = getPriority(i1);
                int priority2 = getPriority(i2);
                if (priority1 != priority2) {
                    return Integer.compare(priority2, priority1); // 高优先级在前
                }

                // 优先级2: 按执行ID排序保证确定性
                return i1.getExecuteId().compareTo(i2.getExecuteId());
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取任务优先级
     *
     * @param inconsistency 状态不一致记录
     * @return 优先级数值(越大越优先)
     */
    private int getPriority(TaskStatusInconsistency inconsistency) {
        switch (inconsistency.getSparkStatus()) {
            case FAILED:
                return 3;
            case KILLED:
                return 3;
            case SUCCEEDED:
                return 2;
            case UNKNOWN:
                return 1;
            default:
                return 0;
        }
    }

    /**
     * 批量修复状态不一致
     *
     * @param inconsistencies 不一致列表
     * @param statistics      统计信息
     */
    private void batchFixInconsistencies(List<TaskStatusInconsistency> inconsistencies,
        RecoveryStatistics statistics) {
        int fixedCount = 0;
        int failedCount = 0;

        for (TaskStatusInconsistency inconsistency : inconsistencies) {
            try {
                boolean fixed = taskStatusRecoveryService.fixTaskStatusInconsistency(inconsistency);
                if (fixed) {
                    fixedCount++;
                    log.info("成功修复任务状态，执行ID: {}", inconsistency.getExecuteId());
                } else {
                    failedCount++;
                    log.warn("修复任务状态失败，执行ID: {}", inconsistency.getExecuteId());
                    statistics.addError("修复失败: " + inconsistency.getExecuteId());
                }
            } catch (Exception e) {
                failedCount++;
                log.error("修复任务状态时发生异常，执行ID: " + inconsistency.getExecuteId(), e);
                statistics.addError("修复异常: " + inconsistency.getExecuteId() + " - " + e.getMessage());
            }

            // 控制修复速度，避免对系统造成压力
            if ((fixedCount + failedCount) % 10 == 0) {
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        statistics.setFixedCount(fixedCount);
        statistics.setFailedCount(failedCount);
    }

    /**
     * 恢复统计信息
     */
    public static class RecoveryStatistics {

        private int candidateCount = 0;
        private int checkedCount = 0;
        private int inconsistencyCount = 0;
        private int fixedCount = 0;
        private int failedCount = 0;
        private List<String> errors = new ArrayList<>();
        private LocalDateTime startTime = LocalDateTime.now();
        private LocalDateTime endTime;
        private boolean enabled = true;

        public static RecoveryStatistics disabled() {
            RecoveryStatistics stats = new RecoveryStatistics();
            stats.enabled = false;
            return stats;
        }

        public void setCandidateCount(int candidateCount) {
            this.candidateCount = candidateCount;
        }

        public void setCheckedCount(int checkedCount) {
            this.checkedCount = checkedCount;
        }

        public void setInconsistencyCount(int inconsistencyCount) {
            this.inconsistencyCount = inconsistencyCount;
        }

        public void setFixedCount(int fixedCount) {
            this.fixedCount = fixedCount;
        }

        public void setFailedCount(int failedCount) {
            this.failedCount = failedCount;
            this.endTime = LocalDateTime.now();
        }

        public void addError(String error) {
            this.errors.add(error);
        }

        public String getSummary() {
            if (!enabled) {
                return "恢复功能已禁用";
            }

            return String.format("候选任务:%d, 已检查:%d, 不一致:%d, 已修复:%d, 失败:%d, 错误:%d",
                candidateCount, checkedCount, inconsistencyCount, fixedCount, failedCount, errors.size());
        }

        public boolean isSuccessful() {
            return enabled && failedCount == 0 && errors.isEmpty();
        }

        public boolean hasInconsistencies() {
            return inconsistencyCount > 0;
        }

        // Getters
        public int getCandidateCount() {
            return candidateCount;
        }

        public int getCheckedCount() {
            return checkedCount;
        }

        public int getInconsistencyCount() {
            return inconsistencyCount;
        }

        public int getFixedCount() {
            return fixedCount;
        }

        public int getFailedCount() {
            return failedCount;
        }

        public List<String> getErrors() {
            return new ArrayList<>(errors);
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public boolean isEnabled() {
            return enabled;
        }
    }
}