# 批处理状态维护系统配置
# M5任务优化配置 - 单节点部署架构优化
# <AUTHOR>
# @since 2025-08-28

# ==== 核心功能开关 ====
# 状态管理总开关
moye.batch.status.management.enabled=true

# 状态检查功能
moye.batch.status.check.enabled=true
moye.batch.status.verification.enabled=true

# ==== BatchTaskMonitor 配置 ====
# 异步验证线程池配置
moye.batch.status.verification.async-pool-size=3
moye.batch.status.verification.cache-first=true

# ==== SparkStatusChecker 配置 ====
# 状态检查配置
moye.batch.status.check.batch-size=10
moye.batch.status.check.use-enhanced-api=true

# ==== TaskLifecycleTracker 配置 ====
# 生命周期跟踪器配置
moye.batch.lifecycle.tracker.enabled=true
moye.batch.lifecycle.tracker.initial-delay=30
moye.batch.lifecycle.tracker.max-tracking-duration=24
moye.batch.lifecycle.tracker.thread-pool-size=5
moye.batch.lifecycle.tracker.cache-integration-enabled=true
moye.batch.lifecycle.tracker.max-concurrent-tasks=100

# ==== TaskStatusCache 配置 ====
# 缓存配置
moye.batch.status.cache.enabled=true
moye.batch.status.cache.expire-minutes=30
moye.batch.status.cache.heartbeat-expire-minutes=10

# 本地缓存配置（单节点优化）
moye.batch.status.cache.local-cache-enabled=true
moye.batch.status.cache.local-cache-max-size=1000
moye.batch.status.cache.local-expire-minutes=5

# ==== SparkLogService 配置 ====
# Spark状态检查配置
moye.batch.spark.status-check.enabled=true
moye.batch.spark.status-check.timeout-seconds=30

# ==== 启动恢复配置 ====
# 启动时恢复配置
moye.batch.startup.recovery.enabled=true
moye.batch.startup.recovery.max-age-hours=72

# ==== 状态恢复配置 ====
# 状态恢复服务配置
moye.batch.status.recovery.enabled=true

# ==== OkHttp 配置 ====
# HTTP客户端配置
moye.batch.okhttp.enabled=true
moye.batch.okhttp.connect-timeout=30
moye.batch.okhttp.read-timeout=60
moye.batch.okhttp.write-timeout=60
moye.batch.okhttp.max-idle-connections=5
moye.batch.okhttp.keep-alive-duration=300

# ==== 性能优化配置 ====
# 单节点部署优化
moye.batch.single-node-optimization=true

# 内存使用优化
moye.batch.memory.optimization.enabled=true

# 日志级别配置
logging.level.com.trs.moye.batch.engine.service=INFO
logging.level.com.trs.moye.batch.engine.service.BatchTaskMonitor=DEBUG
logging.level.com.trs.moye.batch.engine.service.TaskLifecycleTracker=DEBUG