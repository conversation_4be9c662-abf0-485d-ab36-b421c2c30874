# moye-batch-engine 架构优化实施计划

## 项目概述

**任务**: 修复moye-batch-engine模块中的架构问题和报错，移除分布式锁依赖，优化单节点部署

**核心目标**:
1. 移除TaskStatusRecoveryService中的分布式锁依赖
2. 评估SparkRestApiClient实现需求和整合方案
3. 基于现有SparkLogService模式优化架构
4. 确保单节点部署的稳定性和可靠性

## 阶段一：架构分析和规划 (已进行中)

### 1.1 现状分析
- **问题识别**: 
  - TaskStatusRecoveryService使用分布式锁但系统为单节点部署
  - SparkRestApiClient与现有SparkLogService功能可能重叠
  - 单节点架构未充分优化同步机制

- **依赖关系**:
  - SparkLogService.java - 现有Spark日志拉取实现
  - TaskStatusRecoveryService.java - 状态恢复服务
  - SparkStatusChecker.java - 状态检查器
  - BatchTaskMonitor.java - 任务监控服务

### 1.2 风险评估
- **高风险**: 移除分布式锁可能影响并发安全性
- **中风险**: SparkRestApiClient整合可能影响现有功能
- **低风险**: 单节点优化对系统扩展性的长期影响

## 阶段二：实施改进

### 2.1 分布式锁移除 (优先级: 高)
**目标**: 将TaskStatusRecoveryService的分布式锁替换为单节点同步机制
**实施步骤**:
1. 分析当前分布式锁的使用场景
2. 设计单节点同步机制 (AtomicBoolean + synchronized)
3. 实现本地锁替换分布式锁
4. 验证并发安全性

### 2.2 SparkRestApiClient评估 (优先级: 中)
**目标**: 评估SparkRestApiClient的必要性，考虑与SparkLogService整合
**实施步骤**:
1. 深入分析SparkLogService的实现模式
2. 评估SparkRestApiClient的功能需求
3. 确定整合方案或独立实现方案
4. 实施选定方案

### 2.3 单节点架构优化 (优先级: 中)
**目标**: 优化单节点部署的同步机制和性能
**实施步骤**:
1. 优化任务状态缓存机制
2. 改进启动恢复流程
3. 增强错误处理和重试机制
4. 优化资源使用和性能

## 阶段三：质量保障

### 3.1 测试验证
- **单元测试**: 验证修改后的服务功能正确性
- **集成测试**: 确保服务间协作正常
- **并发测试**: 验证单节点同步机制的安全性
- **性能测试**: 确保优化后性能不降低

### 3.2 回归验证
- 验证现有功能完整性
- 确保任务状态一致性
- 检查日志记录和监控功能

## 成功标准

### 功能标准
- [ ] TaskStatusRecoveryService成功移除分布式锁依赖
- [ ] SparkRestApiClient整合或优化完成
- [ ] 所有现有功能保持完整
- [ ] 任务状态监控正常工作

### 质量标准
- [ ] 通过所有现有测试套件
- [ ] 符合项目编码规范 (checkstyle)
- [ ] 并发安全性得到保障
- [ ] 性能不低于原有实现

### 文档标准
- [ ] 更新相关代码注释 (作者: zhang.anlin)
- [ ] 更新CLAUDE.md文档
- [ ] 记录架构变更说明

## 时间计划

- **阶段一** (规划): 1工作日
- **阶段二** (实施): 2-3工作日
- **阶段三** (验证): 1-2工作日
- **总计**: 4-6工作日

## 回滚计划

如果出现关键问题：
1. 保留原有代码的git分支
2. 准备快速回滚脚本
3. 监控系统稳定性指标
4. 制定紧急修复流程