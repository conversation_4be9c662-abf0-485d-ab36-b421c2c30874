package com.trs.moye.storage.engine.db.hybase;

import com.trs.hybase.client.TRSDatabaseColumn;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.exception.BizException;
import java.util.EnumMap;
import java.util.Objects;

/**
 * moye对应海贝字段类型映射
 *
 * <AUTHOR>
 * @since 2024/10/15 14:53
 */
public class FieldTypeHybaseMapping {

    // 私有构造函数，防止实例化
    private FieldTypeHybaseMapping() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static final EnumMap<FieldType, Integer> HYBASE_FIELD_MAPPING = new EnumMap<>(FieldType.class);

    static {
        // 日期类型
        HYBASE_FIELD_MAPPING.put(FieldType.DATE, TRSDatabaseColumn.TYPE_DATE);
        HYBASE_FIELD_MAPPING.put(FieldType.DATETIME, TRSDatabaseColumn.TYPE_DATE);

        // 字符串类型
        HYBASE_FIELD_MAPPING.put(FieldType.CHAR, TRSDatabaseColumn.TYPE_CHAR);
        HYBASE_FIELD_MAPPING.put(FieldType.STRING, TRSDatabaseColumn.TYPE_CHAR);
        HYBASE_FIELD_MAPPING.put(FieldType.TEXT, TRSDatabaseColumn.TYPE_DOCUMENT);

        // 整型类型
        HYBASE_FIELD_MAPPING.put(FieldType.SHORT, TRSDatabaseColumn.TYPE_NUMBER);
        HYBASE_FIELD_MAPPING.put(FieldType.INT, TRSDatabaseColumn.TYPE_NUMBER);
        HYBASE_FIELD_MAPPING.put(FieldType.LONG, TRSDatabaseColumn.TYPE_NUMBER);

        // 小数
        HYBASE_FIELD_MAPPING.put(FieldType.FLOAT, TRSDatabaseColumn.TYPE_NUMBER);
        HYBASE_FIELD_MAPPING.put(FieldType.DOUBLE, TRSDatabaseColumn.TYPE_NUMBER);
        HYBASE_FIELD_MAPPING.put(FieldType.DECIMAL, TRSDatabaseColumn.TYPE_NUMBER);

        // tag类型
        HYBASE_FIELD_MAPPING.put(FieldType.TAG, TRSDatabaseColumn.TYPE_CHAR);
        HYBASE_FIELD_MAPPING.put(FieldType.OBJECT, TRSDatabaseColumn.TYPE_OBJECT);

        // 布尔类型
        HYBASE_FIELD_MAPPING.put(FieldType.BOOLEAN, TRSDatabaseColumn.TYPE_BOOL);
    }

    /**
     * 通过moye字段类型获取hybase对应的字段类型
     *
     * @param fieldType 字段类型
     * @return 对应的字段类型
     */
    public static Integer getType(FieldType fieldType) throws BizException {
        if (Objects.isNull(HYBASE_FIELD_MAPPING.get(fieldType))) {
            throw new BizException("海贝数据库不支持该字段类型:" + fieldType.getLabel());
        }
        return HYBASE_FIELD_MAPPING.get(fieldType);
    }
}
