package com.trs.moye.storage.engine.seatunnel.job.config.source;

import static com.trs.moye.base.data.source.enums.RequestMethod.GET;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.moye.base.data.source.enums.RequestMethod;
import com.trs.moye.base.data.source.setting.http.PageInfo;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * Http source config
 *
 * <p>Http source config is used to specify the configuration of the Http source</p>
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class HttpSourceConfig extends SeatunnelSourceConfig {

    @NotBlank(message = "url 不能为空")
    private String url;

    @JsonProperty("content_field")
    @Builder.Default
    private String contentField = "$";

    @JsonProperty("format")
    @Builder.Default
    private String format = "json";

    @JsonProperty("method")
    @Builder.Default
    private RequestMethod method = GET;

    @JsonProperty("headers")
    private Map<String, String> headers;

    @JsonProperty("params")
    private Map<String, Object> params;

    @JsonProperty("body")
    private Map<String, Object> body;

    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Http";

    @JsonProperty("pageing")
    private PageInfo page;

    @JsonProperty("retry")
    @Builder.Default
    private Integer retry = 1024;

    @Builder.Default
    @JsonProperty("is_encrypted")
    private Boolean isEncrypted = false;
}
