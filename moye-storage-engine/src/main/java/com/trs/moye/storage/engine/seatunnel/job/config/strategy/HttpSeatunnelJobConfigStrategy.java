package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.setting.http.HttpDataSourceSettings;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.SeatunnelSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.HttpSourceConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.HttpSourceConfig.HttpSourceConfigBuilder;
import com.trs.moye.storage.engine.service.impl.ApiServiceImpl;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Http 数据源配置
 */
@Component
public class HttpSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Value("${api.connection.isEncrypted:false}")
    private Boolean isEncrypted = false;

    @Override
    public HttpSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        HttpDataSourceSettings settings = (HttpDataSourceSettings) dataSourceConfig.getSettings();
        //如果开启鉴权，需要提前请求到token
        Map<String, Object> context = ApiServiceImpl.createRequestContext(settings, incrementInfo);
        settings.getRequestParams().getRequestBody().buildDefaultPage();
        //构建请求参数
        HttpSourceConfigBuilder<?, ?> builder = HttpSourceConfig.builder().url(settings.getBase().getUrl())
            .schema(Schema.of(fields)).method(settings.getBase().getMethod())
            .contentField(settings.getRequestParams().getResultPath());
        Map<String, Object> queryParams = settings.getRequestParams().createQueryParams();
        Map<String, Object> body = settings.getRequestParams().createBody(context);
        Map<String, String> headers = settings.getRequestParams().createHeaders(context);
        if (!headers.isEmpty()) {
            builder.headers(headers);
        }
        if (!queryParams.isEmpty()) {
            builder.params(queryParams);
        }
        if (!body.isEmpty()) {
            builder.body(body);
        }
        if (!settings.getRequestParams().getPage().isEmpty()) {
            builder.page(settings.getRequestParams().getPage());
        }
        builder.isEncrypted(isEncrypted);
        return builder.build();
    }

    @Override
    public SeatunnelSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        throw new UnsupportedOperationException("Connection Type Http , Not supported yet.");
    }
}
